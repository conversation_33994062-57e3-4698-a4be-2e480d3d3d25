import React, { useEffect, useState, useCallback } from "react";
import AdMobBanner from "@/components/ads/AdMobBanner";
import { useAdBannerVisibility } from "@/hooks/useAdBannerVisibility";
import { useTermsAcceptance } from "@/hooks/useTermsAcceptance";
import TermsAcceptanceModal from "@/components/modals/TermsAcceptanceModal";
import { initializeOAuthListeners } from "@/services/authService";

interface AppLayoutProps {
  children: React.ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const { shouldShowBanner } = useAdBannerVisibility();
  const { needsAcceptance, isLoading, acceptTerms, declineTerms } =
    useTermsAcceptance();
  const [showRefreshModal, setShowRefreshModal] = useState(false);
  const [isBannerLoaded, setIsBannerLoaded] = useState(false);

  // Callback per gestire lo stato del banner
  const handleAdLoaded = useCallback((loaded: boolean) => {
    setIsBannerLoaded(loaded);
  }, []);

  // Inizializza i listener OAuth all'avvio
  useEffect(() => {
    console.log("🚀 Inizializzazione AppLayout - Setup listener OAuth...");

    // Inizializza i listener per OAuth (solo per mobile)
    initializeOAuthListeners();
  }, []);

  // Mostra uno spinner discreto solo se il caricamento dura più di 100ms (evita flash)
  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-amber-50">
        <div className="text-center">
          {/* Spinner più piccolo e discreto */}
          <div className="w-12 h-12 mx-auto mb-3 flex items-center justify-center">
            <div
              className="w-6 h-6 border-3 border-amber-300 border-t-amber-600 rounded-full animate-spin"
              style={{
                filter: "drop-shadow(0 2px 4px rgba(0,0,0,0.2))",
              }}
            ></div>
          </div>
          <p className="text-amber-700 font-medium text-sm">Caricamento...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="h-screen flex flex-col">
        <div className="flex-1 overflow-auto">{children}</div>
      </div>

      {/* Banner AdMob fisso in fondo - z-index 30 per essere sotto il footer (z-50) */}
      {shouldShowBanner && (
        <div className="fixed bottom-0 left-0 right-0 z-30">
          <AdMobBanner onAdLoaded={handleAdLoaded} className="w-full" />
        </div>
      )}

      {/* Modale di accettazione termini - appare solo se necessario */}
      <TermsAcceptanceModal
        isOpen={needsAcceptance}
        onAccept={acceptTerms}
        onDecline={declineTerms}
      />

      {/* Modale di refresh post-login */}
      {/* <RefreshModal
        isOpen={showRefreshModal}
        onRefresh={() => {
          setShowRefreshModal(false);
          window.location.reload();
        }}
        onDismiss={() => setShowRefreshModal(false)}
      /> */}
    </>
  );
};

export default AppLayout;
