var $e=Object.defineProperty;var Pe=(r,e,n)=>e in r?$e(r,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):r[e]=n;var ee=(r,e,n)=>Pe(r,typeof e!="symbol"?e+"":e,n);import{a as re}from"./audioManager-oMWoQbcF.js";/*! Capacitor: https://capacitorjs.com/ - MIT License */var H;(function(r){r.Unimplemented="UNIMPLEMENTED",r.Unavailable="UNAVAILABLE"})(H||(H={}));class ne extends Error{constructor(e,n,o){super(e),this.message=e,this.code=n,this.data=o}}const Ne=r=>{var e,n;return r!=null&&r.androidBridge?"android":!((n=(e=r==null?void 0:r.webkit)===null||e===void 0?void 0:e.messageHandlers)===null||n===void 0)&&n.bridge?"ios":"web"},ve=r=>{const e=r.CapacitorCustomPlatform||null,n=r.Capacitor||{},o=n.Plugins=n.Plugins||{},t=()=>e!==null?e.name:Ne(r),i=()=>t()!=="web",a=u=>{const d=g.get(u);return!!(d!=null&&d.platforms.has(t())||s(u))},s=u=>{var d;return(d=n.PluginHeaders)===null||d===void 0?void 0:d.find(m=>m.name===u)},c=u=>r.console.error(u),g=new Map,l=(u,d={})=>{const m=g.get(u);if(m)return console.warn(`Capacitor plugin "${u}" already registered. Cannot register plugins twice.`),m.proxy;const A=t(),h=s(u);let T;const E=async()=>(!T&&A in d?T=typeof d[A]=="function"?T=await d[A]():T=d[A]:e!==null&&!T&&"web"in d&&(T=typeof d.web=="function"?T=await d.web():T=d.web),T),N=(V,P)=>{var C,$;if(h){const I=h==null?void 0:h.methods.find(R=>P===R.name);if(I)return I.rtype==="promise"?R=>n.nativePromise(u,P.toString(),R):(R,f)=>n.nativeCallback(u,P.toString(),R,f);if(V)return(C=V[P])===null||C===void 0?void 0:C.bind(V)}else{if(V)return($=V[P])===null||$===void 0?void 0:$.bind(V);throw new ne(`"${u}" plugin is not implemented on ${A}`,H.Unimplemented)}},y=V=>{let P;const C=(...$)=>{const I=E().then(R=>{const f=N(R,V);if(f){const p=f(...$);return P=p==null?void 0:p.remove,p}else throw new ne(`"${u}.${V}()" is not implemented on ${A}`,H.Unimplemented)});return V==="addListener"&&(I.remove=async()=>P()),I};return C.toString=()=>`${V.toString()}() { [capacitor code] }`,Object.defineProperty(C,"name",{value:V,writable:!1,configurable:!1}),C},L=y("addListener"),U=y("removeListener"),v=(V,P)=>{const C=L({eventName:V},P),$=async()=>{const R=await C;U({eventName:V,callbackId:R},P)},I=new Promise(R=>C.then(()=>R({remove:$})));return I.remove=async()=>{console.warn("Using addListener() without 'await' is deprecated."),await $()},I},b=new Proxy({},{get(V,P){switch(P){case"$$typeof":return;case"toJSON":return()=>({});case"addListener":return h?v:L;case"removeListener":return U;default:return y(P)}}});return o[u]=b,g.set(u,{name:u,proxy:b,platforms:new Set([...Object.keys(d),...h?[A]:[]])}),b};return n.convertFileSrc||(n.convertFileSrc=u=>u),n.getPlatform=t,n.handleError=c,n.isNativePlatform=i,n.isPluginAvailable=a,n.registerPlugin=l,n.Exception=ne,n.DEBUG=!!n.DEBUG,n.isLoggingEnabled=!!n.isLoggingEnabled,n},Le=r=>r.Capacitor=ve(r),q=Le(typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{}),pe=q.registerPlugin;class Ae{constructor(){this.listeners={},this.retainedEventArguments={},this.windowListeners={}}addListener(e,n){let o=!1;this.listeners[e]||(this.listeners[e]=[],o=!0),this.listeners[e].push(n);const i=this.windowListeners[e];i&&!i.registered&&this.addWindowListener(i),o&&this.sendRetainedArgumentsForEvent(e);const a=async()=>this.removeListener(e,n);return Promise.resolve({remove:a})}async removeAllListeners(){this.listeners={};for(const e in this.windowListeners)this.removeWindowListener(this.windowListeners[e]);this.windowListeners={}}notifyListeners(e,n,o){const t=this.listeners[e];if(!t){if(o){let i=this.retainedEventArguments[e];i||(i=[]),i.push(n),this.retainedEventArguments[e]=i}return}t.forEach(i=>i(n))}hasListeners(e){return!!this.listeners[e].length}registerWindowListener(e,n){this.windowListeners[n]={registered:!1,windowEventName:e,pluginEventName:n,handler:o=>{this.notifyListeners(n,o)}}}unimplemented(e="not implemented"){return new q.Exception(e,H.Unimplemented)}unavailable(e="not available"){return new q.Exception(e,H.Unavailable)}async removeListener(e,n){const o=this.listeners[e];if(!o)return;const t=o.indexOf(n);this.listeners[e].splice(t,1),this.listeners[e].length||this.removeWindowListener(this.windowListeners[e])}addWindowListener(e){window.addEventListener(e.windowEventName,e.handler),e.registered=!0}removeWindowListener(e){e&&(window.removeEventListener(e.windowEventName,e.handler),e.registered=!1)}sendRetainedArgumentsForEvent(e){const n=this.retainedEventArguments[e];n&&(delete this.retainedEventArguments[e],n.forEach(o=>{this.notifyListeners(e,o)}))}}const ce=r=>encodeURIComponent(r).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape),le=r=>r.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent);class Ve extends Ae{async getCookies(){const e=document.cookie,n={};return e.split(";").forEach(o=>{if(o.length<=0)return;let[t,i]=o.replace(/=/,"CAP_COOKIE").split("CAP_COOKIE");t=le(t).trim(),i=le(i).trim(),n[t]=i}),n}async setCookie(e){try{const n=ce(e.key),o=ce(e.value),t=`; expires=${(e.expires||"").replace("expires=","")}`,i=(e.path||"/").replace("path=",""),a=e.url!=null&&e.url.length>0?`domain=${e.url}`:"";document.cookie=`${n}=${o||""}${t}; path=${i}; ${a};`}catch(n){return Promise.reject(n)}}async deleteCookie(e){try{document.cookie=`${e.key}=; Max-Age=0`}catch(n){return Promise.reject(n)}}async clearCookies(){try{const e=document.cookie.split(";")||[];for(const n of e)document.cookie=n.replace(/^ +/,"").replace(/=.*/,`=;expires=${new Date().toUTCString()};path=/`)}catch(e){return Promise.reject(e)}}async clearAllCookies(){try{await this.clearCookies()}catch(e){return Promise.reject(e)}}}pe("CapacitorCookies",{web:()=>new Ve});const we=async r=>new Promise((e,n)=>{const o=new FileReader;o.onload=()=>{const t=o.result;e(t.indexOf(",")>=0?t.split(",")[1]:t)},o.onerror=t=>n(t),o.readAsDataURL(r)}),be=(r={})=>{const e=Object.keys(r);return Object.keys(r).map(t=>t.toLocaleLowerCase()).reduce((t,i,a)=>(t[i]=r[e[a]],t),{})},Me=(r,e=!0)=>r?Object.entries(r).reduce((o,t)=>{const[i,a]=t;let s,c;return Array.isArray(a)?(c="",a.forEach(g=>{s=e?encodeURIComponent(g):g,c+=`${i}=${s}&`}),c.slice(0,-1)):(s=e?encodeURIComponent(a):a,c=`${i}=${s}`),`${o}&${c}`},"").substr(1):null,Ue=(r,e={})=>{const n=Object.assign({method:r.method||"GET",headers:r.headers},e),t=be(r.headers)["content-type"]||"";if(typeof r.data=="string")n.body=r.data;else if(t.includes("application/x-www-form-urlencoded")){const i=new URLSearchParams;for(const[a,s]of Object.entries(r.data||{}))i.set(a,s);n.body=i.toString()}else if(t.includes("multipart/form-data")||r.data instanceof FormData){const i=new FormData;if(r.data instanceof FormData)r.data.forEach((s,c)=>{i.append(c,s)});else for(const s of Object.keys(r.data))i.append(s,r.data[s]);n.body=i;const a=new Headers(n.headers);a.delete("content-type"),n.headers=a}else(t.includes("application/json")||typeof r.data=="object")&&(n.body=JSON.stringify(r.data));return n};class Fe extends Ae{async request(e){const n=Ue(e,e.webFetchExtra),o=Me(e.params,e.shouldEncodeUrlParams),t=o?`${e.url}?${o}`:e.url,i=await fetch(t,n),a=i.headers.get("content-type")||"";let{responseType:s="text"}=i.ok?e:{};a.includes("application/json")&&(s="json");let c,g;switch(s){case"arraybuffer":case"blob":g=await i.blob(),c=await we(g);break;case"json":c=await i.json();break;case"document":case"text":default:c=await i.text()}const l={};return i.headers.forEach((u,d)=>{l[d]=u}),{data:c,headers:l,status:i.status,url:i.url}}async get(e){return this.request(Object.assign(Object.assign({},e),{method:"GET"}))}async post(e){return this.request(Object.assign(Object.assign({},e),{method:"POST"}))}async put(e){return this.request(Object.assign(Object.assign({},e),{method:"PUT"}))}async patch(e){return this.request(Object.assign(Object.assign({},e),{method:"PATCH"}))}async delete(e){return this.request(Object.assign(Object.assign({},e),{method:"DELETE"}))}}pe("CapacitorHttp",{web:()=>new Fe});var M=(r=>(r.Coins="coins",r.Cups="cups",r.Swords="swords",r.Clubs="clubs",r))(M||{}),k=(r=>(r.Ace="A",r.Two="2",r.Three="3",r.Four="4",r.Five="5",r.Six="6",r.Seven="7",r.Jack="J",r.Horse="H",r.King="K",r))(k||{});const We={coins:"Denari",cups:"Coppe",swords:"Spade",clubs:"Bastoni"},Be={A:"asso",3:"tre",2:"due",K:"re",H:"cavallo",J:"fante",7:"setta",6:"sei",5:"cinque",4:"quattro"},ae=()=>{const r=[];return Object.values(M).forEach(e=>{Object.values(k).forEach(n=>{let o=0,t=0;switch(n){case"3":o=10,t=3;break;case"2":o=9,t=3;break;case"A":o=8,t=10;break;case"K":o=7,t=3;break;case"H":o=6,t=3;break;case"J":o=5,t=3;break;case"7":o=4,t=0;break;case"6":o=3,t=0;break;case"5":o=2,t=0;break;case"4":o=1,t=0;break}const i=`${Be[n]} di ${We[e]}`,a=Ge();r.push({id:a,suit:e,rank:n,displayName:i,order:o,value:t})})}),r},he=r=>{const e=[...r];for(let n=e.length-1;n>0;n--){const o=Math.floor(Math.random()*(n+1));[e[n],e[o]]=[e[o],e[n]]}return e},Ce=(r,e,n)=>{const o=Array(e).fill([]).map(()=>[]);for(let t=0;t<n;t++)for(let i=0;i<e;i++)if(r.length>0){const a=r.pop();o[i]=[...o[i],a]}return o},De=(r,e)=>{if(!e)return r;const n=r.filter(o=>o.suit===e);return n.length>0?n:r},xe=(r,e,n)=>{if(r.length===0)throw new Error("Cannot determine winner of empty trick");const o=r.filter(i=>i.suit===n);if(o.length>0)return o.reduce((i,a)=>a.order>i.order?a:i,o[0]);const t=r.filter(i=>i.suit===e);return t.length>0?t.reduce((i,a)=>a.order>i.order?a:i,t[0]):r.reduce((i,a)=>a.order>i.order?a:i,r[0])},Ge=()=>"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(r){const e=Math.random()*16|0;return(r==="x"?e:e&3|8).toString(16)}),Nr={coins:"/images/semi/denari.png",cups:"/images/semi/coppe.png",swords:"/images/semi/spade.png",clubs:"/images/semi/bastoni.png"},vr=(r,e,n)=>{if(!e.trumpSuit||r.suit!==e.trumpSuit||r.rank!==k.Ace||e.lastTrumpSelector!==n||e.trickNumber!==1)return!1;const o=e.players[n].hand;return Te(o,e.trumpSuit)},Te=(r,e)=>[k.Ace,k.Two,k.Three].every(o=>r.some(t=>t.suit===e&&t.rank===o)),te=(r,e)=>["A","2","3"].every(o=>r.some(t=>t.suit===e&&t.rank===o)),Y=()=>!q.isNativePlatform(),Lr=()=>{Y()&&console.warn(`🔍 DEBUG MODE ATTIVO!

Puoi vedere le carte di tutti i giocatori.
Per disattivare, imposta VITE_DEBUG_SHOW_ALL_CARDS=false nel file .env

Questo è utile per:
- Capire la logica delle decisioni CPU
- Verificare se l'AI risponde correttamente agli assi
- Testare le strategie collaborative
- Debug delle strategie anti-spreco
- Forzare la maraffa per testare la logica`)},de=()=>Y()?localStorage.getItem("debug_force_player_maraffa")==="true":!1,ge=()=>Y()?localStorage.getItem("debug_force_cpu_maraffa")==="true":!1,Ze=(r,e)=>{Y()&&console.log(`🎯 [DEBUG MARAFFA] Maraffa forzata per giocatore ${r+1} con seme ${e}`)},z=(r,e,n=M.Coins)=>{const o=[...r],t=[...o[e]],i=[{suit:n,rank:k.Ace,id:`${n}_${k.Ace}_forced`},{suit:n,rank:k.Two,id:`${n}_${k.Two}_forced`},{suit:n,rank:k.Three,id:`${n}_${k.Three}_forced`}],s=[...t.filter(c=>c.suit!==n).slice(0,7),...i];for(;s.length<10;){const c=[M.Cups,M.Swords,M.Clubs].filter(u=>u!==n),g=c[Math.floor(Math.random()*c.length)],l=[k.Four,k.Five,k.Six,k.Seven][Math.floor(Math.random()*4)];s.push({suit:g,rank:l,id:`${g}_${l}_filler_${s.length}`})}return o[e]=s.slice(0,10),Ze(e,n),o},Oe=r=>{let e=[...r];if(de()&&(e=z(e,0,M.Coins)),ge()){const n=Math.floor(Math.random()*3)+1,t=[M.Cups,M.Swords,M.Clubs][n-1];e=z(e,n,t)}return de()&&ge()&&(console.log("🎯 [DEBUG MARAFFA] Modalità TUTTI HANNO MARAFFA attiva - semi diversi per ogni giocatore"),e=z(e,1,M.Cups),e=z(e,2,M.Swords),e=z(e,3,M.Clubs)),e},Se=r=>{const e=[M.Coins,M.Cups,M.Swords,M.Clubs];for(const n of e)if(Te(r,n))return n;return null},Q=r=>{if(r.gamePhase!=="selectTrump")return{hasMaraffa:!1,maraffaSuit:null,playerIndex:-1};const e=r.players[r.currentPlayer],n=Se(e.hand);return{hasMaraffa:n!==null,maraffaSuit:n,playerIndex:r.currentPlayer}},He=r=>{const e=Q(r);return!e.hasMaraffa||!e.maraffaSuit?r:X(r,e.maraffaSuit)},Ie=(r=31)=>{const e=he(ae()),n=[{id:0,name:"Tu",hand:[],team:0,position:"south"},{id:1,name:"Avversèri 1",hand:[],team:1,position:"east"},{id:2,name:"Cumpagn",hand:[],team:0,position:"north"},{id:3,name:"Avversèri 2",hand:[],team:1,position:"west"}];let o=Ce(e,4,10);o=Oe(o),n.forEach((l,u)=>{l.hand=o[u]});const t=[{id:0,players:[n[0],n[2]],score:0,tricksWon:[],currentRoundPoints:0,currentRoundFigures:0},{id:1,players:[n[1],n[3]],score:0,tricksWon:[],currentRoundPoints:0,currentRoundFigures:0}],i=n.findIndex(l=>l.hand.some(u=>u.suit===M.Coins&&u.rank===k.Four)),a=i!==-1?i:0,c=Q({players:n,currentPlayer:a}),g={deck:e,players:n,teams:t,currentPlayer:a,trumpSuit:null,leadSuit:null,currentTrick:[],trickNumber:1,gamePhase:"selectTrump",playerWithFourOfCoins:i,roundScore:[0,0],gameScore:[0,0],lastTrickWinner:null,announcedAction:null,message:"Il giocatore con il 4 di Denari deve scegliere la briscola",leadPlayer:a,currentRoundScoreHistory:{team0:[],team1:[]},victoryPoints:r,lastTrumpSelector:null,maraffeMade:[0,0],maxScoreDifference:0,automaticMaraffa:c.hasMaraffa?c:null};return c.hasMaraffa&&c.maraffaSuit&&a!==0?X(g,c.maraffaSuit):g},X=(r,e)=>{if(r.gamePhase!=="selectTrump")return r;const n=r.players[r.currentPlayer],o=[...r.teams],t=[...r.maraffeMade];let i=!1;return te(n.hand,e)?(i=!0,console.log(`� MARAFFA RILEVATA! Team ${n.team} ha A+2+3 di ${e}. I punti verranno assegnati quando l'Asso sarà giocato come prima carta.`)):console.log(`❌ NESSUNA MARAFFA: Team ${n.team} non ha A+2+3 di ${e}`),{...r,trumpSuit:e,gamePhase:"play",teams:o,maraffeMade:t,automaticMaraffa:null,message:`La briscola è ${e===M.Coins?"Denari":e===M.Cups?"Coppe":e===M.Swords?"Spade":"Bastoni"}. ${r.players[r.currentPlayer].name} inizia.${i?" Maraffa rilevata! I punti saranno assegnati quando l'Asso verrà giocato.":""}`,leadPlayer:r.currentPlayer,lastTrumpSelector:r.currentPlayer}},ke=(r,e)=>{if(r.gamePhase!=="play"||r.currentTrick.length!==0)return r;const n=r.players[r.currentPlayer];return console.log(`[STRATEGIC ANNOUNCEMENT] ${n.name} dichiara: ${e.toUpperCase()}`),{...r,announcedAction:e,message:`${n.name} dichiara: "${e.toUpperCase()}"`}},ue=r=>{const e=r.filter(i=>i.rank===k.Ace).length,n=r.filter(i=>[k.Three,k.Two,k.King,k.Horse,k.Jack].includes(i.rank)).length,o=e,t=n;return(o>0||t>0)&&console.log(`    🧮 Calcolo: ${e} assi = ${o} punti, ${n} figure`),{acePoints:o,figureCount:t,totalPoints:o+Math.floor(t/3)}},ze=(r,e)=>{const n=r.players[e].team,o=[...r.teams];if(r.currentTrick.length!==4)return console.warn(`⚠️ ERRORE: Tentativo di calcolare punteggio con trick incompleto (${r.currentTrick.length} carte)`),r;const t=ue(r.currentTrick);console.log(`🎯 PRESA ${r.trickNumber}:`),console.log("   Carte giocate:",r.currentTrick.map(a=>`${a.rank} di ${a.suit}`)),console.log(`   Punti da assi: ${t.acePoints}`),console.log(`   Figure trovate: ${t.figureCount}`),console.log(`   Team vincitore: ${n}`);const i={team0:[...r.currentRoundScoreHistory.team0],team1:[...r.currentRoundScoreHistory.team1]};if(n===0){const a=o[0].currentRoundPoints,s=o[0].currentRoundFigures;o[0].currentRoundPoints+=t.acePoints;const c=s+t.figureCount,g=Math.floor(c/3);o[0].currentRoundPoints+=g,o[0].currentRoundFigures=c%3,console.log(`   Team 0: ${a} + ${t.acePoints} (assi) + ${g} (da ${c} figure) = ${o[0].currentRoundPoints}`),console.log(`   Team 0: Figure rimanenti: ${o[0].currentRoundFigures}`),i.team0.push(t.acePoints+g)}else{const a=o[1].currentRoundPoints,s=o[1].currentRoundFigures;o[1].currentRoundPoints+=t.acePoints;const c=s+t.figureCount,g=Math.floor(c/3);o[1].currentRoundPoints+=g,o[1].currentRoundFigures=c%3,console.log(`   Team 1: ${a} + ${t.acePoints} (assi) + ${g} (da ${c} figure) = ${o[1].currentRoundPoints}`),console.log(`   Team 1: Figure rimanenti: ${o[1].currentRoundFigures}`),i.team1.push(t.acePoints+g)}if(o[n].tricksWon.push(...r.currentTrick),r.trickNumber===10){const a=o[n].currentRoundPoints;o[n].currentRoundPoints+=1,console.log(`🏆 ULTIMA PRESA - Team ${n}: ${a} + 1 (bonus) = ${o[n].currentRoundPoints}`),n===0?i.team0.push(1):i.team1.push(1),console.log(`📊 FINE MANO - Totali: Team 0 = ${o[0].currentRoundPoints}, Team 1 = ${o[1].currentRoundPoints}`),console.log(`📊 SOMMA TOTALE: ${o[0].currentRoundPoints+o[1].currentRoundPoints} punti`),console.log(`📊 Figure rimaste: Team 0 = ${o[0].currentRoundFigures}, Team 1 = ${o[1].currentRoundFigures}`)}return{...r,teams:o,currentRoundScoreHistory:i}},oe=(r,e)=>{if(r.gamePhase!=="play")return r;const n=r.players[r.currentPlayer],o=n.hand.findIndex(c=>c.id===e);if(o===-1)return r;const t=n.hand[o];if(r.trumpSuit&&r.trickNumber===1&&r.currentTrick.length===0&&te(n.hand,r.trumpSuit)&&r.lastTrumpSelector===r.currentPlayer&&(t.suit!==r.trumpSuit||t.rank!=="A"))return{...r,message:"🎯 MARAFFA! Devi giocare l'Asso della briscola come prima carta."};if(r.currentTrick.length===0){const c=[...n.hand];c.splice(o,1);const g=[...r.players];g[r.currentPlayer]={...n,hand:c};let l=0;const u=[...r.maraffeMade],d=[...r.teams];if(r.trumpSuit&&r.trickNumber===1&&r.currentTrick.length===0&&t.suit===r.trumpSuit&&t.rank==="A"&&r.lastTrumpSelector===r.currentPlayer&&te(n.hand,r.trumpSuit)){const T=n.hand.some(y=>y.suit===r.trumpSuit&&y.rank==="A"),E=n.hand.some(y=>y.suit===r.trumpSuit&&y.rank==="2"),N=n.hand.some(y=>y.suit===r.trumpSuit&&y.rank==="3");T&&E&&N?(l=3,u[n.team]+=1,d[n.team].currentRoundPoints+=l,console.log(`🎯🎯🎯 MARAFFA COMPLETATA! ${n.name} (Team ${n.team}) ha giocato l'Asso di briscola come prima carta. +${l} punti bonus! Round points aggiornati: ${d[n.team].currentRoundPoints}`)):console.log(`❌ MARAFFA FALSA: ${n.name} non ha tutte le carte della Maraffa!`)}const m=[r.roundScore[0]+(n.team===0?l:0),r.roundScore[1]+(n.team===1?l:0)],A=Math.abs(m[0]-m[1]),h=Math.max(r.maxScoreDifference,A);return{...r,players:g,teams:d,currentTrick:[t],leadSuit:t.suit,leadPlayer:r.currentPlayer,currentPlayer:(r.currentPlayer+1)%4,roundScore:m,maraffeMade:u,maxScoreDifference:h,announcedAction:null,message:l>0?`${n.name} ha giocato ${t.displayName} e ottenuto il bonus Maraffa (+3 punti)!`:`${n.name} ha giocato ${t.displayName}`,maraffaCompleted:l>0?{playerIndex:r.currentPlayer,team:n.team,bonus:l}:void 0}}if(r.leadSuit&&t.suit!==r.leadSuit&&n.hand.some(c=>c.suit===r.leadSuit))return{...r,message:`Devi giocare una carta di ${r.leadSuit===M.Coins?"Denari":r.leadSuit===M.Cups?"Coppe":r.leadSuit===M.Swords?"Spade":"Bastoni"} se ne hai una.`};const i=[...n.hand];i.splice(o,1);const a=[...r.players];a[r.currentPlayer]={...n,hand:i};const s=[...r.currentTrick,t];if(s.length===4){const c=xe(s,r.leadSuit,r.trumpSuit),g=s.findIndex(h=>h.id===c.id),l=(r.leadPlayer+g)%4;r.players[l].team;const u=[...r.teams],d=ze({...r,players:a,teams:u,currentTrick:s},l),m=d.teams[0].currentRoundPoints,A=d.teams[1].currentRoundPoints;if(r.trickNumber===10){const h=[r.gameScore[0]+m,r.gameScore[1]+A],T=(h[0]>=r.victoryPoints||h[1]>=r.victoryPoints)&&h[0]!==h[1];return{...d,currentTrick:[],leadSuit:null,currentPlayer:l,leadPlayer:l,trickNumber:1,gamePhase:T?"gameOver":"roundOver",lastTrickWinner:l,roundScore:[m,A],gameScore:h,message:T?`Gioco finito! ${h[0]>h[1]?"Squadra 1":"Squadra 2"} ha vinto!`:h[0]>=r.victoryPoints&&h[1]>=r.victoryPoints&&h[0]===h[1]?`Entrambe le squadre hanno raggiunto ${r.victoryPoints} punti ma sono in pareggio (${h[0]}-${h[1]}). La partita continua fino al primo vincitore!`:`${r.players[l].name} ha vinto l'ultima presa. Punteggio mano: ${m}-${A}`}}return{...d,currentTrick:[],leadSuit:null,currentPlayer:l,leadPlayer:l,trickNumber:r.trickNumber+1,lastTrickWinner:l,message:`${r.players[l].name} ha vinto la presa. Punteggio attuale: Squadra 1 (${m.toFixed(1)}) - Squadra 2 (${A.toFixed(1)})`}}return{...r,players:a,currentTrick:s,currentPlayer:(r.currentPlayer+1)%4,message:`${n.name} ha giocato ${t.displayName}`}},_e=r=>{if(r.gamePhase!=="scoring"&&r.gamePhase!=="roundOver")return r;const e=he(ae()),n=[...r.players];let o=Ce(e,4,10);o=Oe(o),n.forEach((g,l)=>{g.hand=o[l]});const t=[{...r.teams[0],tricksWon:[],currentRoundPoints:0,currentRoundFigures:0},{...r.teams[1],tricksWon:[],currentRoundPoints:0,currentRoundFigures:0}],i=n.findIndex(g=>g.hand.some(l=>l.suit===M.Coins&&l.rank===k.Four));let a=0;r.lastTrumpSelector===null?a=i!==-1?i:0:a=(r.lastTrumpSelector+1)%4;const s={...r,players:n,currentPlayer:a},c=Q(s);if(c.hasMaraffa&&c.maraffaSuit&&a!==0){const g={...r,deck:e,players:n,teams:t,currentPlayer:a,leadPlayer:a,trumpSuit:null,leadSuit:null,currentTrick:[],trickNumber:1,gamePhase:"selectTrump",playerWithFourOfCoins:i,roundScore:[0,0],lastTrickWinner:null,announcedAction:null,currentRoundScoreHistory:{team0:[],team1:[]},automaticMaraffa:c};return X(g,c.maraffaSuit)}return{...r,deck:e,players:n,teams:t,currentPlayer:a,leadPlayer:a,trumpSuit:null,leadSuit:null,currentTrick:[],trickNumber:1,gamePhase:"selectTrump",playerWithFourOfCoins:i,roundScore:[0,0],lastTrickWinner:null,announcedAction:null,currentRoundScoreHistory:{team0:[],team1:[]},automaticMaraffa:c.hasMaraffa?c:null,message:c.hasMaraffa&&a===0?`${n[a].name} ha la Maraffa! Puoi scegliere il seme per la briscola.`:r.lastTrumpSelector===null?`${n[a].name} ha il 4 di Denari e deve scegliere la briscola`:`${n[a].name} deve scegliere la briscola`}},Ke=(r,e)=>{if(r.gamePhase!=="play")return r;const n=r.teams[e],o=r.gameScore[e],t=ue(n.tricksWon),i=t.acePoints+Math.floor(t.figureCount/3);return o+i>=r.victoryPoints?(console.log("🎯 gameLogic: Partita finita - vittoria squadra",e+1),{...r,gamePhase:"gameOver",gameScore:[e===0?o+i:r.gameScore[0],e===1?o+i:r.gameScore[1]],message:`Squadra ${e+1} ha dichiarato vittoria e ha raggiunto i punti necessari.`}):(console.log("🎯 gameLogic: Partita finita - dichiarazione fallita squadra",e+1),{...r,gamePhase:"gameOver",roundScore:e===0?[0,11]:[11,0],gameScore:[r.gameScore[0]+(e===0?0:11),r.gameScore[1]+(e===1?0:11)],message:`Squadra ${e+1} ha dichiarato vittoria ma non ha abbastanza punti. Squadra ${e===0?2:1} vince 11-0.`})},Je=(r=31)=>Ie(r),Vr=Object.freeze(Object.defineProperty({__proto__:null,announceAction:ke,applyAutomaticMaraffa:He,calculateScore:ue,checkForAutomaticMaraffa:Q,declareGameWin:Ke,findAutomaticMaraffa:Se,initializeGameState:Ie,playCard:oe,selectTrump:X,startNewGame:Je,startNewRound:_e},Symbol.toStringTag,{value:"Module"}));var G=(r=>(r[r.EASY=0]="EASY",r[r.MEDIUM=1]="MEDIUM",r[r.HARD=2]="HARD",r))(G||{});const ie=()=>ae(),W=r=>({3:10,2:9,A:8,K:7,H:6,J:5,7:4,6:3,5:2,4:1})[r.rank||""]||0,D=r=>{switch(r.rank){case k.Ace:return 1;case k.Three:case k.Two:case k.King:case k.Horse:case k.Jack:return .3;default:return 0}},_=r=>r.rank===k.Ace||r.rank===k.King||r.rank===k.Horse||r.rank===k.Jack,K=r=>r.filter(_),me=(r,e,n)=>{const o=n.trumpSuit,t=n.leadSuit;if(r.suit===o&&r.rank===k.Three)return!0;if(r.suit===o){const i=[];r.rank!==k.Three&&i.push(k.Three),r.rank!==k.Two&&r.rank!==k.Three&&i.push(k.Two),r.rank!==k.Ace&&r.rank!==k.Two&&r.rank!==k.Three&&i.push(k.Ace);const a=e.playedBySuit[o]||[];return i.every(c=>a.some(g=>g.rank===c))}if(t&&r.suit===t){const i=[],a=W(r),s=[k.Three,k.Two,k.Ace,k.King,k.Horse,k.Jack,"7","6","5","4"];for(const d of s)W({rank:d,suit:r.suit})>a&&i.push(d);const c=e.playedBySuit[t]||[],g=i.every(d=>c.some(m=>m.rank===d)),u=(e.playedBySuit[o]||[]).length>=10;return g&&u}return!1},J=r=>({3:10,2:9,A:8,K:7,H:6,J:5,7:4,6:3,5:2,4:1})[r.rank]||0,j=(r,e,n,o)=>{if(!e||e.length===0)return!0;const t=o&&r.suit===o,i=e.filter(a=>o&&a.suit===o);if(t){if(i.length===0)return!0;{const a=i.reduce((s,c)=>W(c)>W(s)?c:s);return W(r)>W(a)}}else{if(i.length>0||!n||r.suit!==n)return!1;const a=e.filter(c=>n&&c.suit===n);if(a.length===0)return!0;const s=a.reduce((c,g)=>W(g)>W(c)?g:c);return W(r)>W(s)}},je=(r,e,n,o)=>n&&r.suit===n?qe(r,e):Ye(r,e,n),qe=(r,e,n)=>{const o=e.trumpsRemaining,t=W(r);return o.filter(a=>W(a)>t).length===0},Ye=(r,e,n)=>{const o=e.remainingCards.filter(a=>a.suit===r.suit),t=W(r);return o.filter(a=>W(a)>t).length>0?!1:n&&r.suit!==n?e.trumpsRemaining.length===0:!0},fe=(r,e,n,o)=>{if(r.length===0)return 0;let t=0,i=r[0];for(let a=1;a<r.length;a++){const s=r[a];Qe(s,i,e,n)&&(t=a,i=s)}return t},Qe=(r,e,n,o)=>o&&r.suit===o&&e.suit===o?W(r)>W(e):o&&r.suit===o&&e.suit!==o?!0:o&&r.suit!==o&&e.suit===o?!1:n&&r.suit===n&&e.suit===n?W(r)>W(e):n&&r.suit===n&&e.suit!==n?!0:(n&&r.suit!==n&&e.suit===n,!1),Xe=(r,e,n)=>{if(!e)return!1;const o=["3","2","A","K","H","J","7","6","5","4"],t=W(r),i=o.filter(c=>{const g={rank:c,suit:r.suit};return W(g)>t}),a=e.playedBySuit[r.suit]||[];return i.every(c=>a.some(g=>g.rank===c))?(console.log(`[DOMINANZA] 🏆 ${r.rank} di ${r.suit} è DOMINANTE! Tutte le carte più forti sono state giocate`),console.log(`[DOMINANZA] Carte più forti giocate: ${i.join(", ")}`),!0):!1},ye=r=>r.trumpSuit?[...r.teams[0].tricksWon,...r.teams[1].tricksWon,...r.currentTrick||[]].filter(n=>n&&n.suit===r.trumpSuit).length:0,er=(r,e,n)=>{if(!e.trumpSuit)return{shouldPlayTrump:!1,recommendedCard:null,reason:"Nessuna briscola selezionata"};const o=r.filter(s=>s.suit===e.trumpSuit);if(o.length<4)return{shouldPlayTrump:!1,recommendedCard:null,reason:`Solo ${o.length} briscole, strategia non applicabile`};const t=ye(e),a=10-t-o.length;if(console.log(`[STRATEGIA MOLTE BRISCOLE] 🎯 Ho ${o.length} briscole, giocate: ${t}, rimaste agli avversari: ~${a}`),a>2){const s=o.filter(g=>!["3","2","A"].includes(g.rank));let c;if(s.length>0)c=s[0];else{const g=new CardEvaluator;c=o.sort((u,d)=>g.getCardOrder(u)-g.getCardOrder(d))[0]}return{shouldPlayTrump:!0,recommendedCard:c,reason:`Strategia molte briscole: gioco ${c.rank} per far finire le briscole agli avversari (~${a} rimaste)`}}return{shouldPlayTrump:!1,recommendedCard:null,reason:`Avversari hanno poche briscole (~${a}), non serve continuare la strategia`}},rr=(r,e,n)=>{if(!e.trumpSuit)return{shouldConserve:!1,cardToAvoid:null,reason:"Nessuna briscola selezionata"};const o=r.filter(m=>m.suit===e.trumpSuit),t=e.trickNumber||1,i=t>=8;if(t===10)return{shouldConserve:!1,cardToAvoid:null,reason:"È l'ultimo turno, gioca tutto"};if(o.filter(m=>["3","2","A"].includes(m.rank)).length===0)return{shouldConserve:!1,cardToAvoid:null,reason:"Non ho briscole alte da conservare"};ye(e);const c=[...e.teams[0].tricksWon,...e.teams[1].tricksWon,...e.currentTrick||[]],g=c.some(m=>m&&m.suit===e.trumpSuit&&m.rank==="3"),l=c.some(m=>m&&m.suit===e.trumpSuit&&m.rank==="2"),u=c.some(m=>m&&m.suit===e.trumpSuit&&m.rank==="A");let d=null;return!g&&o.some(m=>m.rank==="3")?d=o.find(m=>m.rank==="3")||null:!l&&o.some(m=>m.rank==="2")?d=o.find(m=>m.rank==="2")||null:!u&&o.some(m=>m.rank==="A")&&(d=o.find(m=>m.rank==="A")||null),d?i?{shouldConserve:!0,cardToAvoid:d,reason:`Conservo ${d.rank} di briscola per l'ultimo turno (turno ${t}/10)`}:{shouldConserve:!1,cardToAvoid:null,reason:"Troppo presto per conservare carte forti"}:{shouldConserve:!1,cardToAvoid:null,reason:"Tutte le briscole forti sono già uscite"}},nr=(r,e)=>{const n=[],o={};return r.forEach(t=>{o[t.suit]||(o[t.suit]=[]),o[t.suit].push(t)}),Object.keys(o).forEach(t=>{const i=o[t],a=i.some(c=>c.rank==="2"),s=i.some(c=>c.rank==="3");a&&s?e.playedCards.some(g=>g.suit===t&&g.rank==="3")?(console.log(`[PRIORITÀ 3 SU 2] ✅ Il 3 di ${t} è già uscito - ora il 2 è sicuro`),n.push(...i)):(console.log(`[PRIORITÀ 3 SU 2] 🎯 Ho sia 2 che 3 di ${t}, il 3 non è uscito - preferisco il 3`),i.forEach(g=>{g.rank!=="2"&&n.push(g)})):n.push(...i)}),n},Re=r=>{const e=ie(),n=[],o={},t={};[M.Coins,M.Cups,M.Swords,M.Clubs].forEach(u=>{o[u]=[]});for(let u=0;u<4;u++)t[u]=[];[...r.teams[0].tricksWon,...r.teams[1].tricksWon].forEach(u=>{u&&(n.push(u),u.suit&&o[u.suit].push(u))}),r.currentTrick&&r.currentTrick.length>0&&r.currentTrick.forEach((u,d)=>{if(u){n.push(u),u.suit&&o[u.suit].push(u);const m=((r.leadPlayer??0)+d)%4;t[m].push(u)}}),console.log(`[MEMORY ANALYSIS] 📊 Carte giocate totali: ${n.length}`),console.log("[MEMORY ANALYSIS] 📊 Dettaglio per seme:",Object.keys(o).map(u=>`${u}: ${o[u].length}`).join(", ")),r.teams&&r.teams.forEach(u=>{u.tricksWon&&Array.isArray(u.tricksWon)&&u.tricksWon.forEach(d=>{Array.isArray(d)&&d.forEach((m,A)=>{if(m&&m.suit&&m.rank){n.push(m),o[m.suit.toString()].push(m);const T=((r.leadPlayer??0)+A)%4;t[T].push(m)}})})}),r.currentTrick&&Array.isArray(r.currentTrick)&&r.currentTrick.forEach((u,d)=>{if(u&&u.suit&&u.rank){n.push(u),o[u.suit.toString()].push(u);const m=((r.leadPlayer??0)+d)%4;t[m].push(u)}});const a=e.filter(u=>!n.some(d=>u.suit===d.suit&&u.rank===d.rank)),s={};[M.Coins,M.Cups,M.Swords,M.Clubs].forEach(u=>{s[u]=a.filter(d=>d.suit===u).length});const c=a.filter(u=>u.suit===r.trumpSuit),g={};return[M.Coins,M.Cups,M.Swords,M.Clubs].forEach(u=>{g[u]=a.filter(d=>d.suit===u&&["A","3","2","K","H","J"].includes(d.rank))}),{playedCards:n,playedBySuit:o,playedByPlayer:t,remainingCards:a,suitDistribution:s,trumpsRemaining:c,highCardsRemaining:g}},se=r=>{const e={playedCards:[],playedBySuit:{},playedByPlayer:{},remainingCards:ie(),suitDistribution:{},trumpsRemaining:[],highCardsRemaining:{}};[M.Coins,M.Cups,M.Swords,M.Clubs].forEach(n=>{e.playedBySuit[n]=[],e.suitDistribution[n]=10,e.highCardsRemaining[n]=ie().filter(o=>o.suit===n&&["A","3","2","K","H","J"].includes(o.rank))});for(let n=0;n<4;n++)e.playedByPlayer[n]=[];return r.currentTrick&&r.currentTrick.length>0&&r.currentTrick.forEach((n,o)=>{e.playedCards.push(n),n.suit&&e.playedBySuit[n.suit.toString()].push(n);const t=((r.leadPlayer??0)+o)%4;e.playedByPlayer[t].push(n)}),e.trumpsRemaining=e.remainingCards.filter(n=>n.suit===r.trumpSuit),e};let x=class{getCardValue(e){switch(e.rank){case k.Ace:return 1;case k.Three:case k.Two:case k.King:case k.Horse:case k.Jack:return .3;default:return 0}}getCardOrder(e){return{3:10,2:9,A:8,K:7,H:6,J:5,7:4,6:3,5:2,4:1}[e.rank]||0}getCardStrengthScore(e,n=!1){const o=this.getCardOrder(e),t=this.getCardValue(e),i=n?5:0;return o+t*2+i}isStrategicCard(e){return e.rank===k.Two}isThreeForOpening(e,n){return e.rank===k.Three&&e.suit!==n}isGoodForTeammate(e){return e.rank===k.Ace||e.rank===k.King||e.rank===k.Horse||e.rank===k.Jack}canWinCurrentTrick(e,n,o,t){if(!n||n.length===0)return!0;const i=e.suit===t;for(const a of n){const s=a.suit===t;if(!(i&&!s)){if(!i&&s)return!1;if(i&&s){if(this.getCardOrder(e)<=this.getCardOrder(a))return!1}else if(e.suit===o&&a.suit===o){if(this.getCardOrder(e)<=this.getCardOrder(a))return!1}else if(e.suit!==o&&a.suit===o)return!1}}return!0}isObviousWinSituation(e,n,o,t,i=0){if(!this.canWinCurrentTrick(e,n,o,t))return!1;if(e.rank==="3"&&e.suit===o&&e.suit!==t||e.rank==="2"&&e.suit===o&&e.suit!==t&&!n.some(g=>g.rank==="3"&&g.suit===o))return!0;const a=e.suit===t;if(!a&&(e.rank==="3"||e.rank==="2")&&(n.length<=1||i>=1||n.length>=2&&!n.some(l=>l.rank==="A"||l.rank==="3"||l.rank==="2")))return!0;if(a&&(e.rank==="3"||e.rank==="2")&&i>=2){const c=n.filter(l=>l.suit===t);return c.length===0?!0:c.filter(l=>this.getCardOrder(l)>this.getCardOrder(e)).length===0}if(e.suit===t&&(e.rank==="3"||e.rank==="2")){const c=n.filter(l=>l.suit===t);return c.length===0?!0:c.filter(l=>this.getCardOrder(l)>this.getCardOrder(e)).length===0}return!1}findLowestValidCard(e,n,o){return this.findBestDiscardCard(e,n,o)}selectOptimalTrumpSuit(e){const n=this.findMaraffa(e);if(n)return n;const o={};Object.values(M).forEach(i=>o[i]=0),e.forEach(i=>o[i.suit]++);const t=Object.entries(o).find(([i,a])=>a>=4);return t?t[0]:this.chooseSuitWithHighestCards(e)}findMaraffa(e){const n={};Object.values(M).forEach(o=>n[o]=[]),e.forEach(o=>n[o.suit].push(o));for(const[o,t]of Object.entries(n)){const i=t.map(a=>a.rank);if(i.includes(k.Ace)&&i.includes(k.Two)&&i.includes(k.Three))return o}return null}chooseSuitWithHighestCards(e){const n={};return Object.values(M).forEach(o=>n[o]=0),e.forEach(o=>{n[o.suit]+=this.getCardStrengthScore(o)}),Object.entries(n).reduce((o,[t,i])=>i>n[o]?t:o,Object.keys(n)[0])}shouldLoadTrick(e,n,o,t,i,a){if(!(e.length===3))return{shouldLoad:!1,reason:"Non ultimo giocatore",loadingCards:[]};const c=this.analyzeCurrentWinner(e,n,o,t,a),g=a.players[i].team;if(!(c.winnerTeam===g&&c.winnerIndex!==(i-t+4)%4))return{shouldLoad:!1,reason:"Compagno non sta vincendo",loadingCards:[]};const u=e.reduce((y,L)=>y+this.getCardValue(L),0),d=a.trickNumber??1,m=d===10,A=d>=8;if(!(u>=1||A||m||u>=.5))return{shouldLoad:!1,reason:`Trucco non abbastanza prezioso (${u} punti)`,loadingCards:[]};const E=a.players[i].hand,N=this.getLoadingCards(E);return{shouldLoad:!0,reason:`Caricamento trucco: compagno vincente, ${u} punti attuali`,loadingCards:N}}getCooperativePlayStrategy(e,n,o,t,i,a,s){if(n.length===0){if(console.log("[STRATEGIA] 🎯 PRIMO A GIOCARE - Strategia intelligente"),(s.trickNumber??1)<=3){const I=e.filter(R=>R.rank==="3"&&R.suit!==t);if(I.length>0){const R=I[0];return console.log(`[STRATEGIA] 🎯 PRIMO (prime mani): Controllo con 3 di ${R.suit}!`),{strategy:"compete",recommendedCard:R,reason:`🎯 CONTROLLO PRIME MANI: 3 di ${R.suit} come primo giocatore per controllare il tavolo`}}}const V=e.filter(I=>this.getCardValue(I)===0&&I.suit!==t&&I.rank!=="2");if(V.length>0){V.sort((R,f)=>this.getCardOrder(f)-this.getCardOrder(R));const I=V[0];return{strategy:"compete",recommendedCard:I,reason:`🎯 APERTURA SICURA: ${I.rank} di ${I.suit} - conservo carte di valore per momenti giusti`}}const P=[...e].sort((I,R)=>this.getCardValue(I)-this.getCardValue(R)),C=P.filter(I=>!(I.rank==="3"&&I.suit===t)),$=C.length>0?C[0]:P[0];return{strategy:"compete",recommendedCard:$,reason:`🎯 APERTURA ULTIMA RISORSA: ${$.rank} di ${$.suit} - evito 3 di briscola`}}const c=this.selectOptimalCardWithSafetyCheck(e,n,o,t,s,a);if(c)return console.log(`[SAFE ACE PRIORITY] ${c.reason}`),{strategy:c.strategy,recommendedCard:c.recommendedCard,reason:c.reason};if(n.some(v=>v.rank==="A")){const v=e.filter(b=>this.canWinCurrentTrick(b,n,o,t));if(v.length>0){v.sort((V,P)=>{const C=this.isStrategicCard(V),$=this.isStrategicCard(P);if(!C&&$)return-1;if(C&&!$)return 1;if(C&&$){if(V.rank==="2"&&P.rank==="3")return-1;if(V.rank==="3"&&P.rank==="2")return 1}return this.getCardStrengthScore(V)-this.getCardStrengthScore(P)});const b=v[0];return{strategy:"compete",recommendedCard:b,reason:`🔥 ASSO SUL TAVOLO (1 punto)! Prendo con ${b.rank} - 1 punto vale QUALSIASI carta!`}}}const l=n.reduce((v,b)=>v+this.getCardValue(b),0);if(l>=1){const v=e.filter(b=>this.canWinCurrentTrick(b,n,o,t));if(v.length>0)return v.sort((b,V)=>this.getCardStrengthScore(b)-this.getCardStrengthScore(V)),{strategy:"compete",recommendedCard:v[0],reason:`💰 ${l.toFixed(1)} PUNTI SUL TAVOLO - Vale qualsiasi carta per vincere!`}}if(l>=.6){const v=se(s),b=this.shouldUseStrategicCardsForPoints(e,n,o,t,l,v,n.length===3);if(b.shouldUse&&b.recommendedCard)return console.log(`[STRATEGIA] ${b.reason}`),{strategy:"compete",recommendedCard:b.recommendedCard,reason:b.reason};const V=e.filter(P=>this.canWinCurrentTrick(P,n,o,t));if(V.length>0){const P=V.filter(C=>!this.isStrategicCard(C));if(P.length>0)return P.sort((C,$)=>this.getCardStrengthScore(C)-this.getCardStrengthScore($)),{strategy:"compete",recommendedCard:P[0],reason:`💰 ${l.toFixed(1)} punti - Prendo con ${P[0].rank}, conservo 2 e 3 per situazioni migliori!`}}console.log(`[STRATEGIA] ${b.reason}`)}const u=this.analyzeCurrentWinner(n,o,t,i,s),d=s.players[a].team,m=u.winnerTeam===d,A=u.winnerTeam!==d&&u.winnerTeam!==-1;if(n.length===3){console.log("[STRATEGIA] 🎯 ULTIMO GIOCATORE - Analisi priorità assoluta");const v=e.filter(P=>this.canWinCurrentTrick(P,n,o,t)&&this.getCardValue(P)>0&&!(P.suit===t&&this.isStrategicCard(P)));if(v.length>0){v.sort((C,$)=>{const I=this.getCardValue(C),R=this.getCardValue($);return I!==R?R-I:this.getCardOrder(C)-this.getCardOrder($)});const P=v[0];return console.log(`[STRATEGIA] 🔥 ULTIMO GIOCATORE - Prendo con ${P.rank} (${this.getCardValue(P)} punti)`),{strategy:"compete",recommendedCard:P,reason:`🔥 ULTIMO GIOCATORE: Prendo sempre con carte di valore! ${P.rank} = ${this.getCardValue(P)} punti garantiti`}}if(l>0){const P=e.filter(C=>this.canWinCurrentTrick(C,n,o,t)&&C.suit!==t);if(P.length>0)return P.sort((C,$)=>this.getCardStrengthScore(C)-this.getCardStrengthScore($)),console.log(`[STRATEGIA] 💰 ULTIMO GIOCATORE - Prendo ${l.toFixed(1)} punti con carta non-briscola`),{strategy:"compete",recommendedCard:P[0],reason:`💰 ULTIMO GIOCATORE: ${l.toFixed(1)} punti sul tavolo - Prendo con ${P[0].rank} non-briscola`}}const b=se(s),V=this.shouldUseStrategicCardsForPoints(e,n,o,t,l,b,!0);if(V.shouldUse&&V.recommendedCard)return console.log(`[STRATEGIA] ${V.reason}`),{strategy:"compete",recommendedCard:V.recommendedCard,reason:V.reason};console.log(`[STRATEGIA] ${V.reason}`)}if(m){console.log("[STRATEGIA] 🎯 TEAM VINCENTE - DEVE DARE PUNTI SUBITO!");const v=e.filter(P=>this.getCardValue(P)>0);if(v.length>0){v.sort((C,$)=>{const I=this.getCardValue(C),R=this.getCardValue($);return C.rank==="A"&&$.rank!=="A"?-1:$.rank==="A"&&C.rank!=="A"?1:I!==R?R-I:this.getCardOrder($)-this.getCardOrder(C)});const P=v[0];return console.log(`[STRATEGIA] 🔥 TEAM VINCENTE: Gioco SUBITO ${P.rank} (${this.getCardValue(P)} punti) - NON accumulo!`),{strategy:"give_points",recommendedCard:P,reason:`🔥 STRATEGIA CORRETTA: Team vince = gioco SUBITO ${P.rank} (${this.getCardValue(P)} pt)! Non accumulo carte con punti!`}}const b=e.filter(P=>this.getCardValue(P)===0&&!this.isStrategicCard(P));if(b.length>0)return b.sort((P,C)=>this.getCardOrder(P)-this.getCardOrder(C)),{strategy:"give_points",recommendedCard:b[0],reason:`🤝 TEAM vincente: Nessuna carta con punti, scarto sicuro ${b[0].rank}`};const V=e.filter(P=>this.isStrategicCard(P));if(V.length>0){const P=V.filter($=>!($.rank==="3"&&$.suit===t)),C=P.length>0?P[0]:V[0];return{strategy:"give_points",recommendedCard:C,reason:`🤝 TEAM vincente: Ultima opzione ${C.rank} - conservo 3 di briscola se possibile`}}return{strategy:"give_points",recommendedCard:e[0],reason:"🤝 TEAM vincente: Fallback"}}if(A){const v=e.filter(V=>this.getCardValue(V)===0);if(v.length>0)return v.sort((V,P)=>this.getCardOrder(V)-this.getCardOrder(P)),{strategy:"discard_low",recommendedCard:v[0],reason:`🤝 COLLABORATIVO: Avversari vincenti, scarto ${v[0].rank} (0 punti)!`};const b=[...e].sort((V,P)=>this.getCardValue(V)-this.getCardValue(P));return{strategy:"discard_low",recommendedCard:b[0],reason:`🤝 COLLABORATIVO: Costretto a dare ${b[0].rank} (${this.getCardValue(b[0])} punti) agli avversari`}}const T=s.trickNumber??1,E=e.filter(v=>this.getCardValue(v)>0),N=E.length>=4,y=T>=4&&T<=6;if(N&&y){console.log(`[STRATEGIA] ⚠️ CONTROLLO ACCUMULO: ${E.length} carte con punti a metà partita`);const v=E.filter(b=>b.rank==="K"||b.rank==="H"||b.rank==="J");if(v.length>=3)return v.sort((b,V)=>this.getCardValue(b)-this.getCardValue(V)),{strategy:"support_passive",recommendedCard:v[0],reason:`⚠️ CONTROLLO ESTREMO: Troppe figure (${v.length}) - gioco ${v[0].rank} per bilanciare mano`}}const L=e.filter(v=>this.canWinCurrentTrick(v,n,o,t));if(L.length>0&&l>=1)return L.sort((v,b)=>this.getCardStrengthScore(v)-this.getCardStrengthScore(b)),{strategy:"compete",recommendedCard:L[0],reason:`🤝 COMPETIZIONE LOGICA: Situazione neutra, prendo ${l.toFixed(1)} punti per il team!`};const U=e.filter(v=>this.getCardValue(v)===0);return U.length>0?{strategy:"support_passive",recommendedCard:U[0],reason:`🤝 COLLABORATIVO: Supporto passivo con ${U[0].rank}`}:{strategy:"support_passive",recommendedCard:e[0],reason:"🤝 COLLABORATIVO: Fallback"}}getDiscardOrderScore(e){return{4:1,5:2,6:3,7:4,J:5,H:6,K:7,2:8,3:9,A:10}[e.rank]||0}findBestDiscardCard(e,n,o){if(!n){const s=e.filter(g=>g.suit!==o);return s.length>0?(s.sort((g,l)=>this.getDiscardOrderScore(g)-this.getDiscardOrderScore(l)),s[0]):[...e].sort((g,l)=>this.getDiscardOrderScore(g)-this.getDiscardOrderScore(l))[0]}const t=e.filter(s=>s.suit===n);if(t.length>0)return t.sort((s,c)=>this.getDiscardOrderScore(s)-this.getDiscardOrderScore(c)),t[0];const i=e.filter(s=>s.suit!==o);return i.length>0?(i.sort((s,c)=>this.getDiscardOrderScore(s)-this.getDiscardOrderScore(c)),i[0]):[...e].sort((s,c)=>this.getDiscardOrderScore(s)-this.getDiscardOrderScore(c))[0]}getLoadingCards(e){const n=e.filter(t=>this.getCardValue(t)>0&&!this.isStrategicCard(t)),o=e.filter(t=>this.isStrategicCard(t));return n.sort((t,i)=>this.getCardValue(i)-this.getCardValue(t)),o.sort((t,i)=>this.getCardValue(i)-this.getCardValue(t)),[...n,...o]}getBestLoadingCard(e,n,o){if(!this.shouldLoadTrick(n,o.leadSuit,o.trumpSuit,o.leadPlayer??0,o.currentPlayer,o).shouldLoad)return null;const i=e.filter(c=>this.getCardValue(c)>0&&!this.isStrategicCard(c));if(i.length>0)return i.sort((c,g)=>this.getCardValue(g)-this.getCardValue(c)),i[0];const a=e.filter(c=>this.getCardValue(c)===0);if(a.length>0)return a[0];const s=e.filter(c=>this.isStrategicCard(c));return s.length>0?(console.warn("[LOADING] ⚠️ Costretto a caricare con carta strategica - situazione subottimale!"),s[0]):null}canPlayAceSafely(e,n,o,t,i,a){if(n.length>0)if(console.log(`[SAFE ACE CHECK] 🚨 Controllo draconiano per asso ${e.suit}`),this.canWinCurrentTrick(e,n,o,t))console.log(`[SAFE ACE CHECK] ✅ ASSO ${e.suit} sicuro perché può vincere`);else{const l=i.players[a].team;let u=0,d=-1,m=!1;for(let E=0;E<n.length;E++){const N=n[E],y=this.getCardOrder(N),L=N.suit===t;L&&!m?(u=E,d=y,m=!0):(L&&m&&y>d||!m&&N.suit===o&&y>d)&&(u=E,d=y)}const A=((i.leadPlayer||0)+u)%4;if(!(i.players[A].team===l))return console.log(`[SAFE ACE CHECK] 🚫 ASSO ${e.suit} NON SICURO - non vince e team non sta vincendo!`),!1;console.log(`[SAFE ACE CHECK] ✅ ASSO ${e.suit} sicuro per supporto team`)}return e.suit===t||!this.canWinCurrentTrick(e,n,o,t)&&n.length>0?!1:4-n.length-1===0||(i.trickNumber??1)===1&&e.suit===o?!0:e.suit===o?!n.some(l=>l.suit===o&&(l.rank==="2"||l.rank==="3")):!1}getSafeAces(e,n,o,t,i,a){return e.filter(s=>s.rank===k.Ace&&this.canPlayAceSafely(s,n,o,t,i,a))}selectOptimalCardWithSafetyCheck(e,n,o,t,i,a){const s=this.getSafeAces(e,n,o,t,i,a);if(s.length>0){console.log(`[SAFE ACES] 🔥 Identificati ${s.length} assi sicuri`);const c=s.filter(u=>this.canWinCurrentTrick(u,n,o,t));if(c.length>0)return{recommendedCard:c[0],reason:`🔥 ASSO SICURO: ${c[0].rank} di ${c[0].suit} può vincere senza rischi`,strategy:"safe_ace_play"};const g=n.length===3,l=n.reduce((u,d)=>u+this.getCardValue(d),0);if(g&&l>0)return{recommendedCard:s[0],reason:`🎯 ASSO SICURO ultimo giocatore: 1 punto garantito + ${l.toFixed(1)} punti sul tavolo`,strategy:"safe_ace_loading"}}return null}analyzeCurrentWinner(e,n,o,t,i){if(e.length===0)return{winnerIndex:-1,winnerTeam:-1,winnerCard:null};let a=0,s=e[0],c=this.calculateCardTrickValue(s,n,o);for(let u=1;u<e.length;u++){const d=e[u],m=this.calculateCardTrickValue(d,n,o);m>c&&(c=m,a=u,s=d)}const g=(t+a)%4,l=i.players[g].team;return{winnerIndex:a,winnerTeam:l,winnerCard:s}}calculateCardTrickValue(e,n,o){return e.suit===o?1e3+this.getCardOrder(e):e.suit===n?this.getCardOrder(e):0}analyzeTeammateCollaborativeStatus(e,n,o,t,i,a){if(e.length===0)return{teammateExists:!1,teammateIsWinning:!1,teammateCanBeBeaten:!1,teammateCard:null,shouldLoadTrick:!1,shouldProtectTeammate:!1,collaborativeAction:"neutral",reason:"Primo a giocare - nessun compagno da analizzare"};const s=a.players[i].team;let c=null,g=-1;for(let N=0;N<e.length;N++){const y=(t+N)%4;if(a.players[y].team===s&&y!==i){c=e[N],g=N;break}}if(!c)return{teammateExists:!1,teammateIsWinning:!1,teammateCanBeBeaten:!1,teammateCard:null,shouldLoadTrick:!1,shouldProtectTeammate:!1,collaborativeAction:"neutral",reason:"Nessun compagno nel trick corrente"};const l=this.analyzeCurrentWinner(e,n,o,t,a),u=l.winnerTeam===s&&l.winnerIndex===g,d=4-e.length-1;let m=!1;u&&d>0&&(m=!this.isCardUnbeatable(c,e,n,o,a));let A,h,T=!1,E=!1;if(u){const N=e.reduce((U,v)=>U+this.getCardValue(v),0),y=e.length===3,L=N>=1;y&&(L||a.trickNumber===10)?(A="support",T=!0,h=`Compagno vincente - valorizzare presa (${N.toFixed(1)} punti)`):m?(A="protect",E=!0,h="Compagno vincente ma vulnerabile - proteggere"):(A="support",h="Compagno sicuramente vincente - supporto passivo")}else this.canWinCurrentTrick(a.players[i].hand[0],e,n,o)?(A="compete",h="Compagno non sta vincendo - posso competere per il team"):(A="neutral",h="Compagno non vincente e non posso aiutare");return{teammateExists:!0,teammateIsWinning:u,teammateCanBeBeaten:m,teammateCard:c,shouldLoadTrick:T,shouldProtectTeammate:E,collaborativeAction:A,reason:h}}isCardUnbeatable(e,n,o,t,i){return e.suit===t&&e.rank==="3"?!0:e.suit===t&&e.rank==="2"?!n.some(s=>s.suit===t&&s.rank==="3"):e.suit===o&&e.rank==="3"?!n.some(s=>s.suit===t):!1}shouldUseStrategicCardsForPoints(e,n,o,t,i,a,s=!1){const c=e.filter(A=>this.isStrategicCard(A)&&this.canWinCurrentTrick(A,n,o,t));if(c.length===0)return{shouldUse:!1,recommendedCard:null,reason:"Nessuna carta strategica può vincere"};if(n.some(A=>A.rank==="A"))return c.sort((A,h)=>A.rank==="2"&&h.rank==="3"?-1:A.rank==="3"&&h.rank==="2"?1:this.getCardStrengthScore(A)-this.getCardStrengthScore(h)),{shouldUse:!0,recommendedCard:c[0],reason:"🔥 ASSO SUL TAVOLO! Vale qualsiasi carta strategica per 1 punto"};const l=c.map(A=>{let h=!1;return a&&a.playedBySuit&&a.playedBySuit[A.suit]&&(h=a.playedBySuit[A.suit].some(T=>T.rank==="A")),{card:A,aceAlreadyPlayed:h,effectiveThreshold:h?.6:Number.MAX_SAFE_INTEGER}}),u=l.filter(A=>i>=A.effectiveThreshold);if(u.length>0){u.sort((T,E)=>{if(T.aceAlreadyPlayed&&!E.aceAlreadyPlayed)return-1;if(!T.aceAlreadyPlayed&&E.aceAlreadyPlayed)return 1;const N=T.card.suit===t,y=E.card.suit===t;return!N&&y?-1:N&&!y?1:T.card.rank==="2"&&E.card.rank==="3"?-1:T.card.rank==="3"&&E.card.rank==="2"?1:this.getCardStrengthScore(T.card)-this.getCardStrengthScore(E.card)});const A=u[0],h=A.aceAlreadyPlayed?`🎯 Asso di ${A.card.suit} già uscito! ${i.toFixed(1)} punti ≥ 0.6 - Uso ${A.card.rank}`:`🔥 ${i.toFixed(1)} punti con Asso sul tavolo - Uso ${A.card.rank}`;return{shouldUse:!0,recommendedCard:A.card,reason:h}}return{shouldUse:!1,recommendedCard:null,reason:l.some(A=>!A.aceAlreadyPlayed)?`💎 Conservo ${c[0].rank} per prendere l'Asso di ${c[0].suit} (${i.toFixed(1)} < soglia Asso)`:`💡 ${i.toFixed(1)} punti < 0.6 - Conservo carte strategiche anche se Assi usciti`}}shouldBeAggressiveForOpponentPrevention(e,n,o,t,i,a){const s=e.reduce((A,h)=>A+this.getCardValue(h),0);if(s<1)return{shouldBeAggressive:!1,trickValue:s,opponentIsWinning:!1,aggressivenessLevel:"none",reason:"Nessun punto sul tavolo - non serve aggressività"};const c=this.analyzeCurrentWinner(e,n,o,t,a);if(c.winnerIndex===-1)return{shouldBeAggressive:!1,trickValue:s,opponentIsWinning:!1,aggressivenessLevel:"none",reason:"Primo a giocare - nessun avversario da contrastare"};const g=a.players[i].team;if(!(c.winnerTeam!==g))return{shouldBeAggressive:!1,trickValue:s,opponentIsWinning:!1,aggressivenessLevel:"none",reason:"Il nostro team sta già vincendo - non serve aggressività"};let u,d;return e.some(A=>A.rank==="A")?(u="critical",d="CRITICO: Avversario sta prendendo un ASSO (1 punto) - IMPEDIRE ASSOLUTAMENTE!"):s>=3?(u="critical",d=`CRITICO: Avversario vince ${s.toFixed(1)} punti - IMPEDIRE A TUTTI I COSTI!`):s>=2?(u="high",d=`ALTO: Avversario vince ${s.toFixed(1)} punti - molto importante impedire`):s>=1.5?(u="medium",d=`MEDIO: Avversario vince ${s.toFixed(1)} punti - importante impedire`):s>=1?(u="medium",d=`MEDIO: Avversario vince ${s.toFixed(1)} punti - importante impedire`):(u="none",d="Troppo pochi punti per giustificare aggressività"),{shouldBeAggressive:u!=="none",trickValue:s,opponentIsWinning:!0,aggressivenessLevel:u,reason:d}}findBestCardToPreventOpponent(e,n,o,t,i){if(o){const s=e.filter(c=>c.suit===o);if(s.length>0){const c=s.filter(g=>this.canWinCurrentTrick(g,n,o,t));if(c.length>0){c.sort((l,u)=>this.getCardOrder(l)-this.getCardOrder(u));const g=c[0];return{recommendedCard:g,strategy:"same_suit",reason:`Uso ${g.rank} del seme per vincere con minimo spreco`}}return{recommendedCard:null,strategy:"cannot_win",reason:"Nessuna carta del seme può vincere"}}}const a=e.filter(s=>s.suit===t);if(a.length>0&&(i==="critical"||i==="high"&&a.some(c=>this.getCardValue(c)===0)||i==="medium"&&a.some(c=>this.getCardValue(c)===0&&this.getCardOrder(c)<=6))){const c=a.filter(g=>this.canWinCurrentTrick(g,n,o,t));if(c.length>0){c.sort((u,d)=>{const m=this.getCardValue(u),A=this.getCardValue(d);return m!==A?m-A:this.getCardOrder(u)-this.getCardOrder(d)});const g=c[0],l=this.getCardValue(g);if((g.rank==="3"||g.rank==="2")&&i!=="critical"){const u=c.filter(d=>d.rank!=="3"&&d.rank!=="2");if(u.length>0){const d=u[0];return{recommendedCard:d,strategy:"trump_cut",reason:`Taglio con ${d.rank} di briscola (evito carte strategiche)`}}}return{recommendedCard:g,strategy:"trump_cut",reason:`Taglio con ${g.rank} di briscola (valore: ${l}, livello: ${i})`}}}return{recommendedCard:null,strategy:"cannot_win",reason:"Impossibile vincere la presa con le carte disponibili"}}getTeammateSupport(e,n,o,t,i,a){if(n.length===0)return{shouldSupport:!1,recommendedCard:null,reason:"Primo giocatore - nessun compagno da supportare",supportType:"NO_SUPPORT"};const s=this.analyzeCurrentWinner(n,o,t,0,i),c=i.players[a].team;if(!(s.winnerTeam===c&&s.winnerIndex!==a))return{shouldSupport:!1,recommendedCard:null,reason:"Compagno non sta vincendo",supportType:"NO_SUPPORT"};const l=n[s.winnerIndex];if(o?e.some(d=>d.suit===o):!1){const d=e.filter(y=>y.suit===o),A=["3","2","A","K","H"].includes(l.rank)||l.suit===t&&l.rank==="J",h=d.filter(y=>!this.canWinCurrentTrick(y,n,o,t)),T=h.filter(y=>this.getCardValue(y)>0);if(T.length>0){T.sort((U,v)=>{const b={A:1,K:2,H:3,J:4,2:5,3:6};return(b[U.rank]||999)-(b[v.rank]||999)});const y=this.getCardValue(T[0]),L=A?"imbattibile":"vincente";return{shouldSupport:!0,recommendedCard:T[0],reason:`Compagno ha ${l.rank} (${L}) - REGALO ${T[0].rank} (${y} punti) SENZA superarlo`,supportType:"GIVE_POINTS"}}const E=h.filter(y=>this.getCardValue(y)===0);if(E.length>0)return E.sort((y,L)=>this.getDiscardOrderScore(y)-this.getDiscardOrderScore(L)),{shouldSupport:!0,recommendedCard:E[0],reason:`Compagno sta vincendo - SCARTO carta senza punti: ${E[0].rank}`,supportType:"BE_STRATEGIC"};const N=d.filter(y=>this.getCardValue(y)>0);return N.sort((y,L)=>this.getDiscardOrderScore(y)-this.getDiscardOrderScore(L)),{shouldSupport:!0,recommendedCard:N[0],reason:`Compagno sta vincendo - SCARTO carta meno dolorosa: ${N[0].rank}`,supportType:"BE_STRATEGIC"}}else{if(!(l.suit===t)){console.log(`[TEAMMATE SUPPORT] 🚫 Compagno vince con ${l.rank} NON-briscola - NO taglio!`);const A=e.filter(h=>h.suit!==t);if(A.length>0){const h=A.filter(N=>this.getCardValue(N)>0);if(h.length>0)return h.sort((N,y)=>{const L={A:1,K:2,H:3,J:4,2:5,3:6};return(L[N.rank]||999)-(L[y.rank]||999)}),{shouldSupport:!0,recommendedCard:h[0],reason:`Compagno ha ${l.rank} NON-briscola - REGALO ${h[0].rank} (${this.getCardValue(h[0])} punti) SENZA tagliare`,supportType:"GIVE_POINTS"};const T=A.filter(N=>this.getCardValue(N)===0);if(T.length>0)return T.sort((N,y)=>this.getDiscardOrderScore(N)-this.getDiscardOrderScore(y)),{shouldSupport:!0,recommendedCard:T[0],reason:`Compagno vince con NON-briscola - NON taglio, uso carta senza punti: ${T[0].rank}`,supportType:"BE_STRATEGIC"};const E=A.filter(N=>this.getCardValue(N)>0);return E.sort((N,y)=>this.getDiscardOrderScore(N)-this.getDiscardOrderScore(y)),{shouldSupport:!0,recommendedCard:E[0],reason:`Compagno vince con NON-briscola - sacrifico ${E[0].rank} SENZA tagliare`,supportType:"BE_STRATEGIC"}}else{console.log("[TEAMMATE SUPPORT] ⚠️ Solo briscole disponibili ma compagno vince con NON-briscola!");const h=e.filter(T=>T.suit===t);return h.sort((T,E)=>{const N=this.getCardValue(T)>0,y=this.getCardValue(E)>0;return!N&&y?-1:N&&!y?1:this.getCardOrder(T)-this.getCardOrder(E)}),{shouldSupport:!0,recommendedCard:h[0],reason:`⚠️ SOLO briscole disponibili - uso la PIÙ DEBOLE: ${h[0].rank} (compagno ha ${l.rank} NON-briscola)`,supportType:"BE_STRATEGIC"}}}const m=e.filter(A=>A.suit!==t);if(m.length>0){const h=["3","2","A","K","H"].includes(l.rank)||l.suit===t&&l.rank==="J",T=m.filter(y=>this.getCardValue(y)>0);if(T.length>0){T.sort((L,U)=>{const v={A:1,K:2,H:3,J:4,2:5,3:6};return(v[L.rank]||999)-(v[U.rank]||999)});const y=h?"imbattibile":"vincente";return{shouldSupport:!0,recommendedCard:T[0],reason:`Compagno ha ${l.rank} (${y}) - REGALO ${T[0].rank} non-briscola (${this.getCardValue(T[0])} punti) SENZA tagliare`,supportType:"GIVE_POINTS"}}const E=m.filter(y=>this.getCardValue(y)===0);if(E.length>0)return E.sort((y,L)=>this.getDiscardOrderScore(y)-this.getDiscardOrderScore(L)),{shouldSupport:!0,recommendedCard:E[0],reason:`Compagno sta vincendo - NON taglio, uso carta senza punti: ${E[0].rank}`,supportType:"BE_STRATEGIC"};const N=m.filter(y=>this.getCardValue(y)>0);return N.sort((y,L)=>this.getDiscardOrderScore(y)-this.getDiscardOrderScore(L)),{shouldSupport:!0,recommendedCard:N[0],reason:`Compagno sta vincendo - non spreco briscole, sacrifico ${N[0].rank}`,supportType:"BE_STRATEGIC"}}else{const A=e.filter(T=>T.suit===t),h=A.filter(T=>!this.canWinCurrentTrick(T,n,o,t));return h.length>0?(h.sort((T,E)=>{const N=this.getCardValue(T)>0,y=this.getCardValue(E)>0;return!N&&y?-1:N&&!y?1:this.getDiscardOrderScore(T)-this.getDiscardOrderScore(E)}),{shouldSupport:!0,recommendedCard:h[0],reason:`Compagno già vincente - uso briscola DEBOLE SENZA superarlo: ${h[0].rank}`,supportType:"BE_STRATEGIC"}):(A.sort((T,E)=>{const N=this.getCardValue(T)>0,y=this.getCardValue(E)>0;return!N&&y?-1:N&&!y?1:this.getDiscardOrderScore(T)-this.getDiscardOrderScore(E)}),{shouldSupport:!0,recommendedCard:A[0],reason:`⚠️ SOLO briscole che superano compagno - uso la PIÙ DEBOLE: ${A[0].rank} (male minore)`,supportType:"BE_STRATEGIC"})}}}isHighUnsuperableCard(e,n){return!!(["3","2","A","K","H"].includes(e.rank)||e.suit===n&&e.rank==="J")}shouldNotWasteAce(e,n,o,t,i){if(e.rank!=="A")return{shouldAvoid:!1,reason:"Non è un asso"};if(this.canWinCurrentTrick(e,o,t.leadSuit,t.trumpSuit))return{shouldAvoid:!1,reason:"L'asso può prendere"};if(this.analyzeTeammatePosition(t,i).teammateIsWinning)return{shouldAvoid:!1,reason:"Il team sta prendendo"};const s=n.filter(c=>c!==e&&this.getCardValue(c)===0);return s.length>0?{shouldAvoid:!0,reason:"Asso sprecato: non prende e regala punti agli avversari",alternative:s[0]}:{shouldAvoid:!1,reason:"Nessuna alternativa disponibile"}}handleNoLeadSuit(e,n,o,t){if(e.some(c=>c.suit===o.leadSuit))return{reason:"Ha il seme di uscita"};const a=this.analyzeTeammatePosition(o,t),s=n.reduce((c,g)=>c+this.getCardValue(g),0);if(a.teammateIsWinning){const c=e.filter(g=>["A","K","Q","J"].includes(g.rank));if(c.length>0)return c.sort((g,l)=>{const u={A:4,K:3,Q:2,J:1};return(u[l.rank]||0)-(u[g.rank]||0)}),{recommendedCard:c[0],reason:`Compagno prende: do ${c[0].rank} di ${c[0].suit}`}}else{if(s>=1){const l=e.filter(u=>u.suit===o.trumpSuit).filter(u=>this.canWinCurrentTrick(u,n,o.leadSuit,o.trumpSuit));if(l.length>0)return l.sort((u,d)=>this.getCardOrder(u)-this.getCardOrder(d)),{recommendedCard:l[0],reason:`Taglio per ${s} punti con ${l[0].rank} di ${l[0].suit}`}}const c=e.filter(g=>["4","5","6","7"].includes(g.rank));if(c.length>0)return{recommendedCard:c[0],reason:`Scarto ${c[0].rank} (non spreco 2 e 3)`}}return{reason:"Nessuna strategia applicabile"}}handleLastPlayer(e,n,o,t){if(!n||n.length!==3)return{reason:"Non è l'ultimo giocatore"};const i=e.filter(a=>this.canWinCurrentTrick(a,n,o.leadSuit,o.trumpSuit));if(i.length>0){const a=i.filter(s=>["A","K","Q","J"].includes(s.rank));if(a.length>0)return a.sort((s,c)=>{const g={A:4,K:3,Q:2,J:1};return(g[c.rank]||0)-(g[s.rank]||0)}),{recommendedCard:a[0],reason:`Ultimo giocatore: prendo con ${a[0].rank} di ${a[0].suit}`}}return{reason:"Non posso prendere con carte di valore"}}handleAceByTeamStatus(e,n,o,t){if(!n||n.length===0)return{reason:"Nessuna presa in corso"};const a=e.filter(g=>["A","K","Q","J"].includes(g.rank)&&g.suit!==o.trumpSuit).filter(g=>this.canWinCurrentTrick(g,n,o.leadSuit,o.trumpSuit));if(a.length>0)return a.sort((g,l)=>{const u={A:4,K:3,Q:2,J:1};return(u[l.rank]||0)-(u[g.rank]||0)}),{recommendedCard:a[0],reason:`Prendo sempre con carte di valore non-briscola: ${a[0].rank} di ${a[0].suit}`};const s=this.analyzeTeammatePosition(o,t),c=e.filter(g=>g.rank==="A");if(s.teammateIsWinning){if(c.length>0)return{recommendedCard:c[0],reason:`Team prende: butto asso ${c[0].rank} di ${c[0].suit}`}}else{const g=e.some(l=>l.rank!=="A");if(c.length>0&&g)return{filteredCards:e.filter(u=>u.rank!=="A"),reason:"Team NON prende: conservo asso, uso alternative"}}return{reason:"Gestione asso non applicabile"}}analyzeTeammatePosition(e,n){if(!e.currentTrick||e.currentTrick.length===0)return{teammateIsWinning:!1};let o=0,t=-1,i=-1,a=!1;for(let l=0;l<e.currentTrick.length;l++){const u=e.currentTrick[l];if(u.suit===e.trumpSuit){a=!0;const d=this.getCardOrder(u);d>t&&(t=d,o=l)}else if(u.suit===e.leadSuit&&!a){const d=this.getCardOrder(u);d>i&&(i=d,o=l)}}const s=((e.leadPlayer??0)+o)%4,c=e.players[s].team,g=e.players[n].team;return{teammateIsWinning:c===g&&s!==n}}applyGameRules(e,n,o){const t=n.currentTrick||[],i=this.handleLastPlayer(e,t,n,o);if(i.recommendedCard)return i;const a=this.handleAceByTeamStatus(e,t,n,o);if(a.recommendedCard)return a;a.filteredCards&&(e=a.filteredCards);const s=this.handleNoLeadSuit(e,t,n,o);if(s.recommendedCard)return s;const c=e.filter(g=>g.rank==="A");for(const g of c){const l=this.shouldNotWasteAce(g,e,t,n,o);if(l.shouldAvoid&&l.alternative)return{recommendedCard:l.alternative,reason:l.reason}}return{reason:"Nessuna regola applicabile, usa logica normale"}}};const tr=(r,e,n,o,t,i=null)=>{const a=D(r),s=n.reduce((c,g)=>c+D(g),0);if(s>=1){if(j(r,n,o.leadSuit,o.trumpSuit))return{isWaste:!1,reason:`REGOLA ASSOLUTA: ${s.toFixed(1)} punti sul tavolo e posso vincere - MAI SPRECO!`,severity:"low",alternativesExist:!1};if(n.length>0&&o.leadPlayer!==void 0){const g=fe(n,o.leadSuit,o.trumpSuit,o.leadPlayer),l=(o.leadPlayer+g)%4,u=o.players[t].team;if(o.players[l].team===u&&l!==t)return{isWaste:!1,reason:`REGOLA ASSOLUTA BIS: ${s.toFixed(1)} punti e compagno vince - aiutare MAI è spreco!`,severity:"low",alternativesExist:!1}}}if(a===0)return{isWaste:!1,reason:"Carta senza valore - sempre sicura da giocare",severity:"low",alternativesExist:!1};if(r.rank==="3"||r.rank==="2"||r.rank==="A"){const c=j(r,n,o.leadSuit,o.trumpSuit);if(c){if(new x().isObviousWinSituation(r,n,o.leadSuit,o.trumpSuit,s))return{isWaste:!1,reason:`Situazione di vincita ovvia con ${r.rank} - sempre giustificato`,severity:"low",alternativesExist:!1};const d=r.suit===o.trumpSuit,m=r.rank==="2",A=r.rank==="3"&&!d;if(m){if(d){if(s>=2)return{isWaste:!1,reason:`Briscola strategica (${r.rank}) giustificata con ${s} punti sul tavolo`,severity:"low",alternativesExist:!1}}else if(s>=2)return{isWaste:!1,reason:`2 strategico giustificato con ${s} punti sul tavolo`,severity:"low",alternativesExist:!1}}else if(A){if(s>=1)return{isWaste:!1,reason:`3 non di briscola OTTIMO per prendere ${s} punti`,severity:"low",alternativesExist:!1};if(n.length<=1)return{isWaste:!1,reason:`3 non di briscola per controllo e apertura (${n.length+1}° a giocare)`,severity:"low",alternativesExist:!1}}if(n.length>=2&&!n.some(T=>T.rank==="A"||T.rank==="3"||T.rank==="2"))return{isWaste:!1,reason:`Carta strategica (${r.rank}) per anticipare possibili assi avversari`,severity:"low",alternativesExist:!1}}let g=!1;if(n.length>0&&o.leadPlayer!==void 0){const l=fe(n,o.leadSuit,o.trumpSuit,o.leadPlayer),u=(o.leadPlayer+l)%4,d=o.players[t].team;g=o.players[u].team===d&&u!==t}if(!c&&!g)return{isWaste:!0,reason:`CRITICO: ${r.rank} andrebbe agli avversari!`,severity:"critical",alternativesExist:e.length>1}}if(i&&Xe(r,i)&&!j(r,n,o.leadSuit,o.trumpSuit)){let g=!1;if(o.leadSuit&&r.suit===o.leadSuit?g=e.filter(u=>u.suit===o.leadSuit).filter(u=>u.id!==r.id).length>0:g=e.filter(l=>l.id!==r.id).length>0,g)return{isWaste:!0,reason:"DOMINANZA: Carta dominante sprecata, ho alternative",severity:"high",alternativesExist:!0}}return s===0?{isWaste:!0,reason:`PRESA SENZA VALORE: Non do ${a} punti per 0 punti`,severity:"medium",alternativesExist:e.some(c=>D(c)===0)}:r.rank==="A"&&s<3?{isWaste:!0,reason:`ASSO: Richiede almeno 3 punti, presa vale ${s}`,severity:"high",alternativesExist:e.some(c=>c.rank!=="A")}:r.rank==="K"&&s<2?{isWaste:!0,reason:`RE: Richiede almeno 2 punti, presa vale ${s}`,severity:"medium",alternativesExist:e.some(c=>c.rank!=="K")}:a>0&&s<a*.8?{isWaste:!0,reason:`VALORE: Carta vale ${a}, presa solo ${s}`,severity:"low",alternativesExist:e.some(c=>D(c)<a)}:{isWaste:!1,reason:"Carta appropriata per questa situazione",severity:"low",alternativesExist:!1}},or=(r,e,n,o)=>{let t=null;try{t=Re(n)}catch(u){console.warn("Errore nell'analisi memoria:",u)}const i=r.map(u=>tr(u,r,e,n,o,t)),a=r.filter((u,d)=>!i[d].isWaste);let s=a;if(a.length===0){console.log("[SISTEMA ANTI-SPRECO] ⚠️ Tutte le carte sono spreco, scelgo male minore");const u=Math.min(...i.map(d=>({low:1,medium:2,high:3,critical:4})[d.severity]));s=r.filter((d,m)=>({low:1,medium:2,high:3,critical:4})[i[m].severity]===u)}let c=null,g="",l=0;if(s.length>0)if(e.length===0)c=s[0],g="Carta strategica per aprire",l=.7;else{const u=s.filter(d=>j(d,e,n.leadSuit,n.trumpSuit));u.length>0?(u.sort((d,m)=>D(d)-D(m)),c=u[0],g="Carta più debole che può vincere",l=.85):(s.sort((d,m)=>D(d)-D(m)),c=s[0],g="Carta di minor valore (non posso vincere)",l=.6)}return{optimalCards:s,wasteAnalysis:i,recommendation:{bestCard:c,reason:g,confidence:l}}},Z=(r,e,n,o)=>{var s,c,g;let t=D(r)*10;r.suit===n.trumpSuit&&(t+=15,[k.Ace,k.Three,k.Two].includes(r.rank)&&(t+=20));const i=e.highCardsRemaining[((s=r.suit)==null?void 0:s.toString())||""]||[];return i.length===1&&i[0].rank===r.rank&&(t+=25),(((g=e.playedBySuit[((c=r.suit)==null?void 0:c.toString())||""])==null?void 0:g.length)||0)<=2&&(t+=10),t},ir=(r,e)=>{if(!r.currentTrick||r.currentTrick.length===0)return{teammateIsWinning:!1,teammatePosition:-1,winningCard:null,shouldSupport:!1};const n=r.players[e].team,o=r.leadPlayer??0;let t=-1;for(let u=0;u<r.currentTrick.length;u++){const d=(o+u)%4;if(r.players[d].team===n&&d!==e){t=u;break}}let i=0,a=r.currentTrick[0],s=0;a.suit===r.trumpSuit?s=1e3+J(a):a.suit===r.leadSuit&&(s=J(a));for(let u=1;u<r.currentTrick.length;u++){const d=r.currentTrick[u];let m=0;d.suit===r.trumpSuit?m=1e3+J(d):d.suit===r.leadSuit&&(m=J(d)),m>s&&(s=m,i=u,a=d)}const c=(o+i)%4,l=(c>=0?r.players[c].team:-1)===n&&c!==e;return{teammateIsWinning:l,teammatePosition:t,winningCard:a,shouldSupport:l&&t>=0}},sr=(r,e,n)=>{const o=r.suit;if(r.rank==="A"){const t=e.some(a=>a.suit===o&&a.rank==="2"),i=e.some(a=>a.suit===o&&a.rank==="3");if(t&&i)return console.log(`[ASSO SICURO] 🎯 Ho la maraffa in ${o} - Asso sicuro!`),!0}else if(r.rank==="K"){const t=e.some(s=>s.suit===o&&s.rank==="A"),i=e.some(s=>s.suit===o&&s.rank==="2"),a=e.some(s=>s.suit===o&&s.rank==="3");if(t&&i&&a)return console.log(`[RE SICURO] 🎯 Ho tutte le carte superiori in ${o} - Re sicuro!`),!0}if(r.rank==="A"){const t=n.some(a=>a.suit===o&&a.rank==="2"),i=n.some(a=>a.suit===o&&a.rank==="3");return t&&i?(console.log(`[ASSO SICURO] 🎯 2 e 3 di ${o} già usciti - Asso sicuro!`),!0):(console.log(`[ASSO NON SICURO] ⚠️ Asso di ${o} non sicuro - 2 e 3 ancora in gioco`),!1)}else if(r.rank==="K"){const t=n.some(s=>s.suit===o&&s.rank==="A"),i=n.some(s=>s.suit===o&&s.rank==="2"),a=n.some(s=>s.suit===o&&s.rank==="3");return t&&i&&a?(console.log(`[RE SICURO] 🎯 A, 2 e 3 di ${o} già usciti - Re sicuro!`),!0):(console.log(`[RE NON SICURO] ⚠️ Re di ${o} non sicuro - A, 2 o 3 ancora in gioco`),!1)}return!1},ar=(r,e,n,o=[])=>{var c;const t=((c=e.currentTrick)==null?void 0:c.reduce((g,l)=>g+n.getCardValue(l),0))||0;if(t>=1){const g=r.filter(l=>{var u,d;return n.canWinCurrentTrick(l,e.currentTrick||[],((d=(u=e.currentTrick)==null?void 0:u[0])==null?void 0:d.suit)||null,e.trumpSuit)});if(g.length>0){g.sort((d,m)=>{const A=n.getCardValue(d),h=n.getCardValue(m);return A===0&&h>0?-1:h===0&&A>0?1:n.getCardOrder(d)-n.getCardOrder(m)});const l=g[0],u=t+n.getCardValue(l);return console.log(`[COOPERATIVA] 🚨 REGOLA ASSOLUTA: ${t.toFixed(1)} punti sul tavolo - PRENDO con ${l.rank} di ${l.suit}!`),{canWin:!0,bestCard:l,pointsSecured:u}}}const i=r.filter(g=>{var l,u;return n.canWinCurrentTrick(g,e.currentTrick||[],((u=(l=e.currentTrick)==null?void 0:l[0])==null?void 0:u.suit)||null,e.trumpSuit)});if(i.length===0)return{canWin:!1,bestCard:null,pointsSecured:0};const a=i.filter(g=>g.suit!==e.trumpSuit&&n.getCardValue(g)>0);if(a.length>0)if(!e.currentTrick||e.currentTrick.length===0){const l=a.filter(u=>u.rank==="A"||u.rank==="K"?sr(u,r,o):!0);if(l.length>0){l.sort((m,A)=>n.getCardValue(A)-n.getCardValue(m));const u=l[0],d=t+n.getCardValue(u);return console.log(`[CARTA VERIFICATA] ✅ Gioco ${u.rank} di ${u.suit} come prima carta - SICURO!`),{canWin:!0,bestCard:u,pointsSecured:d}}else return console.log("[CARTE BLOCCATE] ❌ Nessuna carta di punti sicura come prima carta - salto strategia punti"),{canWin:!1,bestCard:null,pointsSecured:0}}else{a.sort((d,m)=>n.getCardValue(m)-n.getCardValue(d));const l=a[0],u=t+n.getCardValue(l);return{canWin:!0,bestCard:l,pointsSecured:u}}const s=i.filter(g=>g.suit===e.trumpSuit);if(s.length>0){s.sort((u,d)=>n.getCardValue(u)-n.getCardValue(d));const g=s[0],l=t+n.getCardValue(g);if(t>=2||e.trickNumber===10)return{canWin:!0,bestCard:g,pointsSecured:l}}return{canWin:!1,bestCard:null,pointsSecured:0}},ur=(r,e,n,o)=>{var m,A,h,T,E,N,y,L,U,v,b,V,P;console.log(`[COLLABORATIVA AVANZATA] 🤝 Avvio strategia TEAM-FIRST per giocatore ${e}`);const t=new x,i=ir(r,e),a=((m=r.currentTrick)==null?void 0:m.length)===3,s=((A=r.currentTrick)==null?void 0:A.reduce((C,$)=>C+t.getCardValue($),0))||0,g=(r.trickNumber??1)>=8;if(console.log(`[COLLABORATIVA AVANZATA] 📊 Posizione: ${a?"ULTIMO":"NON ULTIMO"}, Presa: ${s.toFixed(1)} punti`),i.teammateIsWinning&&r.currentTrick&&r.currentTrick.length>0){const C=(h=r.currentTrick[0])==null?void 0:h.suit,$=C?n.some(f=>f.suit===C):!1;console.log(`[COLLABORATIVA AVANZATA] 🚨 RILEVATO COMPAGNO VINCENTE: ${(T=i.winningCard)==null?void 0:T.rank} di ${(E=i.winningCard)==null?void 0:E.suit}`),console.log(`[COLLABORATIVA AVANZATA] 📊 Presa attuale: ${s.toFixed(1)} punti, Posso seguire seme: ${$?"SÌ":"NO"}`);const I=n.filter(f=>f.suit===r.trumpSuit),R=n.filter(f=>f.suit!==r.trumpSuit);if($){const f=n.filter(p=>p.suit===C);if(f.length>0){console.log(`[COLLABORATIVA AVANZATA] ✅ SEGUO SEME: ${f.length} carte di ${C} disponibili`);const p=f.filter(O=>_(O));if(p.length>0){p.sort((S,w)=>{const F={A:1,K:2,H:3,J:4};return(F[S.rank]||999)-(F[w.rank]||999)});const O=p[0];return console.log(`[COLLABORATIVA AVANZATA] 🎯 REGALO PUNTI SEGUENDO SEME: ${O.rank} di ${O.suit} (${t.getCardValue(O)} punti)`),{strategy:"support",recommendedCard:O,reason:`🎁 SUPPORTO OTTIMALE: Compagno vincente → regalo ${O.rank} del seme richiesto (${t.getCardValue(O)} punti)`}}return f.sort((O,S)=>t.getDiscardOrderScore(O)-t.getDiscardOrderScore(S)),console.log(`[COLLABORATIVA AVANZATA] 🎯 SCARTO SEGUENDO SEME: ${f[0].rank} di ${f[0].suit}`),{strategy:"support",recommendedCard:f[0],reason:`🎯 SUPPORTO PULITO: Compagno vincente → scarto ${f[0].rank} seguendo il seme`}}}if(!$&&R.length>0){console.log(`[COLLABORATIVA AVANZATA] 🛡️ DIVIETO BRISCOLE: ${R.length} carte non-briscola disponibili, ${I.length} briscole VIETATE`);const f=R.filter(O=>_(O));if(f.length>0){f.sort((S,w)=>{const F={A:1,K:2,H:3,J:4};return(F[S.rank]||999)-(F[w.rank]||999)});const O=f[0];return console.log(`[COLLABORATIVA AVANZATA] 🎯 ANTI-SPRECO PUNTI: ${O.rank} di ${O.suit} (${t.getCardValue(O)} punti) invece di sprecare briscole`),{strategy:"support",recommendedCard:O,reason:`🛡️ ANTI-SPRECO CRITICO: Compagno vincente → regalo ${O.rank} non-briscola (${t.getCardValue(O)} punti) invece di sprecare briscole`}}const p=R.filter(O=>t.getCardValue(O)===0);if(p.length>0)return p.sort((O,S)=>t.getDiscardOrderScore(O)-t.getDiscardOrderScore(S)),console.log(`[COLLABORATIVA AVANZATA] 🎯 ANTI-SPRECO SCARTO: ${p[0].rank} di ${p[0].suit} invece di sprecare briscole`),{strategy:"support",recommendedCard:p[0],reason:`🛡️ ANTI-SPRECO TOTALE: Compagno vincente → scarto ${p[0].rank} non-briscola invece di sprecare briscole`};if(R.length>0)return R.sort((O,S)=>t.getDiscardOrderScore(O)-t.getDiscardOrderScore(S)),console.log(`[COLLABORATIVA AVANZATA] ⚠️ SACRIFICIO MINIMO: ${R[0].rank} di ${R[0].suit} per evitare briscole`),{strategy:"support",recommendedCard:R[0],reason:`⚠️ SACRIFICIO MINIMO: Compagno vincente → sacrifico ${R[0].rank} non-briscola per conservare le briscole`}}if(I.length>0&&R.length===0){if(console.log("[COLLABORATIVA AVANZATA] ⚠️ CASO ESTREMO: Solo briscole disponibili, compagno sta vincendo"),s>=1){const f=I.filter(p=>t.getCardValue(p)===0);if(f.length>0)return f.sort((p,O)=>t.getCardStrengthScore(p)-t.getCardStrengthScore(O)),console.log(`[COLLABORATIVA AVANZATA] 🎯 BRISCOLA DEBOLE GIUSTIFICATA: ${f[0].rank} di ${f[0].suit} per ${s.toFixed(1)} punti`),{strategy:"support",recommendedCard:f[0],reason:`🎯 BRISCOLA GIUSTIFICATA: Solo briscole disponibili, uso la più debole ${f[0].rank} per ${s.toFixed(1)} punti in presa`}}return I.sort((f,p)=>t.getDiscardOrderScore(f)-t.getDiscardOrderScore(p)),console.log(`[COLLABORATIVA AVANZATA] 😞 BRISCOLA FORZATA: ${I[0].rank} di ${I[0].suit} (nessuna alternativa)`),{strategy:"support",recommendedCard:I[0],reason:`😞 BRISCOLA FORZATA: Compagno vincente ma nessuna alternativa disponibile, uso la briscola più debole ${I[0].rank}`}}}const l=ar(n,r,t,o.playedCards);if(l.canWin&&l.bestCard)return console.log("[COLLABORATIVA AVANZATA] 🎯 PRIORITÀ ASSOLUTA 0: POSSO VINCERE CON CARTE DI PUNTI!"),console.log(`[COLLABORATIVA AVANZATA] 💰 PUNTI ASSICURATI AL TEAM: ${l.pointsSecured.toFixed(1)} punti con ${l.bestCard.rank} di ${l.bestCard.suit}`),{strategy:"compete",recommendedCard:l.bestCard,reason:`🎯 PUNTI ASSICURATI: ${l.pointsSecured.toFixed(1)} punti per il team con ${l.bestCard.rank} di ${l.bestCard.suit}! ${l.bestCard.suit===r.trumpSuit?"Briscola strategica":"Carta di punti perfetta"}`};if(i.teammateIsWinning&&i.winningCard){const C=i.winningCard,$=(y=(N=r.currentTrick)==null?void 0:N[0])==null?void 0:y.suit,I=C.rank==="3"||C.rank==="2"||C.rank==="A",R=$?n.some(f=>f.suit===$):!0;if(I&&!R){console.log(`[COLLABORATIVA AVANZATA] 🚨 REGOLA SUPREMA: Compagno vince con ${C.rank} forte e non posso seguire ${$} - DEVO DARE PUNTI!`);const f=n.filter(p=>p.rank==="A"||p.rank==="K"||p.rank==="H"||p.rank==="J");if(f.length>0){f.sort((O,S)=>{const w={A:4,K:3,H:2,J:1};return(w[S.rank]||0)-(w[O.rank]||0)});const p=f[0];return console.log(`[COLLABORATIVA AVANZATA] 🎯 PUNTI AL COMPAGNO: ${p.rank} di ${p.suit} (valore: ${t.getCardValue(p)}) per carta forte ${C.rank}`),{strategy:"support",recommendedCard:p,reason:`🚨 REGOLA SUPREMA: Compagno vince con ${C.rank} forte, non posso seguire ${$} → SEMPRE dare punti! (${p.rank})`}}}}if(i.teammateIsWinning&&s>=1){console.log(`[COLLABORATIVA AVANZATA] 🚨 REGOLA ASSOLUTA BIS: Compagno vince ${s.toFixed(1)} punti - AIUTO SEMPRE!`);const C=n.filter($=>_($));if(C.length>0){C.sort((I,R)=>I.rank==="A"&&R.rank!=="A"?-1:R.rank==="A"&&I.rank!=="A"?1:t.getCardValue(R)-t.getCardValue(I));const $=C[0];return console.log(`[COLLABORATIVA AVANZATA] 🤝 AIUTO COMPAGNO: ${$.rank} di ${$.suit} (valore: ${t.getCardValue($)})`),{strategy:"support",recommendedCard:$,reason:`REGOLA ASSOLUTA: Aiuto compagno che vince ${s.toFixed(1)} punti con ${$.rank}`}}}if(a&&i.teammateIsWinning){const C=r.trickNumber<=3;if(console.log(`[COLLABORATIVA AVANZATA] 🎯 PRIORITÀ ASSOLUTA 1 ATTIVATA: Ultimo giocatore + Team vincente! ${C?"(PRIME MANI - COOPERAZIONE CRITICA!)":""}`),i.winningCard&&i.winningCard.rank==="3"&&i.winningCard.suit!==r.trumpSuit){const p=n.find(O=>O.rank==="A"&&O.suit===i.winningCard.suit);if(p)return console.log(`[COLLABORATIVA AVANZATA] 🏆 ASSO PRIORITARIO ULTIMO: Compagno ha giocato 3 di ${i.winningCard.suit}, gioco Asso di ${p.suit}! ${C?"(STRATEGIA PRIME MANI PERFETTA!)":""}`),{strategy:"support",recommendedCard:p,reason:`🏆 RICONOSCIMENTO 3 (ULTIMO): Compagno ha giocato 3 di ${i.winningCard.suit} per prendere - gioco Asso per massimizzare punti! ${C?"(STRATEGIA PRIME MANI!)":""}`}}if(i.winningCard&&t.getCardValue(i.winningCard)>0){console.log(`[COLLABORATIVA AVANZATA] 🔥 COMPAGNO PRENDE CON PUNTI: ${i.winningCard.rank} (${t.getCardValue(i.winningCard)} punti)`);const p=n.filter(S=>S.rank==="A");if(p.length>0){const S=p[0];return console.log(`[COLLABORATIVA AVANZATA] 🏆 ASSO PRIORITARIO: Compagno prende con punti, gioco Asso di ${S.suit}!`),{strategy:"support",recommendedCard:S,reason:`🏆 ASSO PRIORITARIO: Compagno prende con carta di punti (${i.winningCard.rank}) = SEMPRE giocare l'Asso! (${t.getCardValue(S)} punto)`}}const O=["J","H","K"];for(const S of O){const w=n.find(F=>F.rank===S);if(w)return console.log(`[COLLABORATIVA AVANZATA] 💎 CARTA ALTERNATIVA: Nessun asso, gioco ${w.rank} di ${w.suit}`),{strategy:"support",recommendedCard:w,reason:`💎 SUPPORTO PRIORITARIO: Compagno prende con punti, gioco ${w.rank} (${t.getCardValue(w)} punti)`}}}const I=K(n).sort((p,O)=>t.getCardValue(O)-t.getCardValue(p));if(I.length>0){const p=I[0];return console.log(`[COLLABORATIVA AVANZATA] 🔥 VALORIZZAZIONE MASSIMA: ${p.rank} di ${p.suit} (${t.getCardValue(p)} punti)`),{strategy:"support",recommendedCard:p,reason:`🎯 TEAM-FIRST P1: Ultimo giocatore + team vincente = SEMPRE valorizzare! Gioco ${p.rank} (${t.getCardValue(p)} punti)`}}const R=n.filter(p=>p.rank==="A");if(R.length>0){const O=R.find(S=>{var w,F;return S.suit===((F=(w=r.currentTrick)==null?void 0:w[0])==null?void 0:F.suit)})||R.sort((S,w)=>t.getCardValue(w)-t.getCardValue(S))[0];return console.log(`[COLLABORATIVA AVANZATA] 🏆 ASSO MASSIMIZZAZIONE: Team vincente + ultimo giocatore = gioco Asso di ${O.suit} per massimizzare punti!`),{strategy:"support",recommendedCard:O,reason:`🏆 MASSIMIZZAZIONE TEAM: Team vincente + ultimo giocatore = SEMPRE giocare l'Asso! (${t.getCardValue(O)} punti)`}}const f=n.sort((p,O)=>t.getCardValue(O)-t.getCardValue(p))[0];return{strategy:"support",recommendedCard:f,reason:`🎯 MASSIMIZZAZIONE PUNTI: Ultimo giocatore + team vincente = gioco carta di maggior valore (${t.getCardValue(f)} punti)`}}if(((L=r.currentTrick)==null?void 0:L.length)===2&&i.teammateIsWinning&&i.winningCard){const C=i.winningCard;if(me(C,o,r)||t.isObviousWinSituation(C,r.currentTrick||[],((v=(U=r.currentTrick)==null?void 0:U[0])==null?void 0:v.suit)||null,r.trumpSuit,s)){const I=r.trickNumber<=3;if(console.log(`[COLLABORATIVA AVANZATA] 🚀 PRIORITÀ ASSOLUTA 2 ATTIVATA: Terzo giocatore + compagno imbattibile! ${I?"(PRIME MANI - COOPERAZIONE CRITICA!)":""}`),C.rank==="3"&&C.suit!==r.trumpSuit){const f=n.find(p=>p.rank==="A"&&p.suit===C.suit);if(f)return console.log(`[COLLABORATIVA AVANZATA] 🏆 ASSO PRIORITARIO TERZO: Compagno ha giocato 3 di ${C.suit}, gioco Asso di ${f.suit}! ${I?"(STRATEGIA PRIME MANI PERFETTA!)":""}`),{strategy:"support",recommendedCard:f,reason:`🏆 RICONOSCIMENTO 3 (TERZO): Compagno ha giocato 3 di ${C.suit} per prendere - gioco Asso per massimizzare punti! ${I?"(STRATEGIA PRIME MANI!)":""}`}}const R=K(n).sort((f,p)=>t.getCardValue(p)-t.getCardValue(f));if(R.length>0){const f=R[0];return console.log(`[COLLABORATIVA AVANZATA] 🚀 VALORIZZAZIONE TERZO GIOCATORE: ${f.rank} di ${f.suit} (${t.getCardValue(f)} punti) - Compagno ha ${C.rank} imbattibile!`),{strategy:"support",recommendedCard:f,reason:`🚀 TEAM-FIRST P2: Terzo giocatore + compagno imbattibile (${C.rank}) = SEMPRE valorizzare! Gioco ${f.rank} (${t.getCardValue(f)} punti)`}}}}if(((b=r.currentTrick)==null?void 0:b.length)===1&&i.teammateIsWinning&&i.winningCard){const C=i.winningCard;if(C.rank==="3"&&C.suit!==r.trumpSuit||C.rank==="2"&&C.suit!==r.trumpSuit||C.suit===r.trumpSuit&&(C.rank==="3"||C.rank==="2")){const I=r.trickNumber<=3;if(console.log(`[COLLABORATIVA AVANZATA] ⚡ PRIORITÀ ASSOLUTA 3 ATTIVATA: Secondo giocatore + compagno primo con carta super forte! ${I?"(PRIME MANI - COOPERAZIONE CRITICA!)":""}`),C.rank==="3"&&C.suit!==r.trumpSuit){const f=n.find(p=>p.rank==="A"&&p.suit===C.suit);if(f)return console.log(`[COLLABORATIVA AVANZATA] 🏆 ASSO PRIORITARIO: Compagno ha giocato 3 di ${C.suit}, gioco Asso di ${f.suit}! ${I?"(STRATEGIA PRIME MANI PERFETTA!)":""}`),{strategy:"support",recommendedCard:f,reason:`🏆 RICONOSCIMENTO 3: Compagno ha giocato 3 di ${C.suit} per prendere - gioco Asso per massimizzare punti! ${I?"(STRATEGIA PRIME MANI!)":""}`}}const R=K(n).filter(f=>{var p,O;return!t.canWinCurrentTrick(f,r.currentTrick||[],((O=(p=r.currentTrick)==null?void 0:p[0])==null?void 0:O.suit)||null,r.trumpSuit)}).sort((f,p)=>t.getCardValue(p)-t.getCardValue(f));if(R.length>0){const f=R[0];return console.log(`[COLLABORATIVA AVANZATA] ⚡ VALORIZZAZIONE SECONDO GIOCATORE: ${f.rank} di ${f.suit} (${t.getCardValue(f)} punti) - Compagno primo con ${C.rank} super forte!`),{strategy:"support",recommendedCard:f,reason:`⚡ TEAM-FIRST P3: Secondo giocatore + compagno primo con carta super forte (${C.rank}) = valorizzare senza competere! Gioco ${f.rank} (${t.getCardValue(f)} punti)`}}}}if(!i.teammateIsWinning&&s>0){if(console.log(`[COLLABORATIVA AVANZATA] 🛡️ AVVERSARI VINCENTI: ${s.toFixed(1)} punti in gioco!`),a){const $=r.trickNumber<=3;console.log(`[COLLABORATIVA AVANZATA] 🔥 ULTIMO GIOCATORE + AVVERSARI VINCENTI! ${$?"(PRIME MANI - RIBALTAMENTO CRITICO!)":""}`);const I=n.filter(R=>R.rank==="A");if(I.length>0){const R=I.filter(f=>{var p,O;return t.canWinCurrentTrick(f,r.currentTrick||[],((O=(p=r.currentTrick)==null?void 0:p[0])==null?void 0:O.suit)||null,r.trumpSuit)});if(R.length>0){const p=R.find(O=>{var S,w;return O.suit===((w=(S=r.currentTrick)==null?void 0:S[0])==null?void 0:w.suit)})||R.sort((O,S)=>t.getCardValue(S)-t.getCardValue(O))[0];return console.log(`[COLLABORATIVA AVANZATA] 🏆 RIBALTAMENTO ASSO: ${p.rank} di ${p.suit} ribalta ${s.toFixed(1)} punti! ${$?"(STRATEGIA PRIME MANI PERFETTA!)":""}`),{strategy:"compete",recommendedCard:p,reason:`🏆 RIBALTAMENTO CRITICO: Ultimo giocatore con Asso di ${p.suit} ribalta ${s.toFixed(1)} punti agli avversari! ${$?"(PRIME MANI!)":""}`}}}}if(s>=1){console.log(`[COLLABORATIVA AVANZATA] 🚨 RECUPERO AGGRESSIVO: ${s.toFixed(1)} punti da recuperare!`);const I=(r.currentTrick||[]).filter(f=>f.suit===r.trumpSuit);if(I.length>0){const f=I.reduce((O,S)=>t.getCardOrder(S)>t.getCardOrder(O)?S:O);if(["4","5","6","7"].includes(f.rank)&&s<2){console.log(`[COLLABORATIVA AVANZATA] 🛡️ ANTI-SPRECO: Avversario ha tagliato con ${f.rank} per solo ${s.toFixed(1)} punti - non ritaglio con briscole strategiche`);const O=n.filter(S=>S.suit!==r.trumpSuit);if(O.length>0){O.sort((w,F)=>t.getDiscardOrderScore(w)-t.getDiscardOrderScore(F));const S=O[0];return console.log(`[COLLABORATIVA AVANZATA] 🗑️ SCARTO INTELLIGENTE: ${S.rank} di ${S.suit} invece di sprecare briscole`),{strategy:"support",recommendedCard:S,reason:`🛡️ ANTI-SPRECO: Avversario ha tagliato con briscola debole per pochi punti - scarto ${S.rank} invece di sprecare briscole strategiche`}}}}const R=n.filter(f=>{var p,O;return t.canWinCurrentTrick(f,r.currentTrick||[],((O=(p=r.currentTrick)==null?void 0:p[0])==null?void 0:O.suit)||null,r.trumpSuit)});if(R.length>0){const f=R.filter(S=>S.suit!==r.trumpSuit);if(f.length>0){f.sort((w,F)=>{const B=Z(w,o,r),Ee=Z(F,o,r);return B-Ee});const S=f[0];return console.log(`[COLLABORATIVA AVANZATA] 🎯 RECUPERO NON-BRISCOLA: ${S.rank} di ${S.suit} per ${s.toFixed(1)} punti`),{strategy:"compete",recommendedCard:S,reason:`🎯 RECUPERO OTTIMALE: Non-briscola ${S.rank} recupera ${s.toFixed(1)} punti senza sprecare briscole!`}}if(s>=1){const S=R.filter(w=>w.suit===r.trumpSuit&&(w.rank==="7"||w.rank==="6"||w.rank==="5"||w.rank==="4"));if(S.length>0){S.sort((F,B)=>t.getCardStrengthScore(F)-t.getCardStrengthScore(B));const w=S[0];return console.log(`[COLLABORATIVA AVANZATA] 🎯 RECUPERO OTTIMALE: Briscola bassa ${w.rank} di ${w.suit} per ${s.toFixed(1)} punti!`),{strategy:"compete",recommendedCard:w,reason:`🎯 RECUPERO OTTIMALE: Briscola bassa ${w.rank} recupera ${s.toFixed(1)} punti dagli avversari!`}}}const O=R.sort((S,w)=>{const F=Z(S,o,r),B=Z(w,o,r);return F-B})[0];return console.log(`[COLLABORATIVA AVANZATA] 🚨 RECUPERO CON: ${O.rank} di ${O.suit} - Salvo ${s.toFixed(1)} punti!`),{strategy:"compete",recommendedCard:O,reason:`🚨 RECUPERO AGGRESSIVO: Avversari vincenti con ${s.toFixed(1)} punti, recupero con ${O.rank}!`}}}if(s===0){console.log("[COLLABORATIVA AVANZATA] 🛡️ PRESA SENZA VALORE: 0 punti - evito briscole a tutti i costi");const $=n.filter(I=>I.suit!==r.trumpSuit);if($.length>0){const I=(P=(V=r.currentTrick)==null?void 0:V[0])==null?void 0:P.suit;if(I?$.some(f=>f.suit===I):!1){const f=$.filter(p=>p.suit===I);return f.sort((p,O)=>t.getDiscardOrderScore(p)-t.getDiscardOrderScore(O)),console.log(`[COLLABORATIVA AVANZATA] 🎯 PRESA 0 PUNTI - SEGUO SEME: ${f[0].rank} di ${f[0].suit}`),{strategy:"support",recommendedCard:f[0],reason:`🛡️ CONSERVAZIONE: Presa da 0 punti → seguo seme con ${f[0].rank} per non sprecare briscole`}}else{const f=$.filter(p=>t.getCardValue(p)===0);return f.length>0?(f.sort((p,O)=>t.getDiscardOrderScore(p)-t.getDiscardOrderScore(O)),console.log(`[COLLABORATIVA AVANZATA] 🎯 PRESA 0 PUNTI - SCARTO PULITO: ${f[0].rank} di ${f[0].suit}`),{strategy:"support",recommendedCard:f[0],reason:`🛡️ SCARTO OTTIMALE: Presa da 0 punti → scarto ${f[0].rank} non-briscola senza valore`}):($.sort((p,O)=>t.getDiscardOrderScore(p)-t.getDiscardOrderScore(O)),console.log(`[COLLABORATIVA AVANZATA] ⚠️ PRESA 0 PUNTI - SACRIFICIO MINIMO: ${$[0].rank} di ${$[0].suit}`),{strategy:"support",recommendedCard:$[0],reason:`⚠️ SACRIFICIO MINIMO: Presa da 0 punti → sacrifico ${$[0].rank} non-briscola per conservare briscole`})}}else{console.log("[COLLABORATIVA AVANZATA] 😞 CASO ESTREMO: Solo briscole per presa da 0 punti - uso la più debole");const I=n.filter(f=>f.suit===r.trumpSuit),R=I.filter(f=>t.getCardValue(f)===0);return R.length>0?(R.sort((f,p)=>t.getCardStrengthScore(f)-t.getCardStrengthScore(p)),console.log(`[COLLABORATIVA AVANZATA] 😞 BRISCOLA DEBOLE FORZATA: ${R[0].rank} di ${R[0].suit}`),{strategy:"support",recommendedCard:R[0],reason:`😞 FORZATURA: Presa da 0 punti ma solo briscole disponibili → uso la più debole ${R[0].rank}`}):(I.sort((f,p)=>t.getDiscardOrderScore(f)-t.getDiscardOrderScore(p)),console.log(`[COLLABORATIVA AVANZATA] 😢 SPRECO FORZATO: ${I[0].rank} di ${I[0].suit} per presa da 0 punti`),{strategy:"support",recommendedCard:I[0],reason:`😢 SPRECO INEVITABILE: Presa da 0 punti, solo briscole con valore disponibili → uso ${I[0].rank}`})}}const C=n.filter($=>t.getCardValue($)===0);if(C.length>0){const $=C.reduce((I,R)=>t.getCardOrder(R)<t.getCardOrder(I)?R:I);return{strategy:"support",recommendedCard:$,reason:`🛡️ PROTEZIONE TEAM: Avversari vincenti con ${s.toFixed(1)} punti, gioco carta senza punti (${$.rank})`}}}if(!i.shouldSupport)return console.log("[COLLABORATIVA AVANZATA] ⚡ Nessun supporto necessario - strategia neutra"),{strategy:"neutral"};if(i.teammateIsWinning){console.log("[COLLABORATIVA AVANZATA] 🏆 COMPAGNO STA VINCENDO - Modalità supporto attivo!");const C=i.winningCard;if(!C)return{strategy:"support"};if(me(C,o,r)){console.log("[COLLABORATIVA AVANZATA] 🔥 COMPAGNO IMBATTIBILE - Preparazione valorizzazione!");const f=n.filter(p=>{var O,S;return!t.canWinCurrentTrick(p,r.currentTrick||[],((S=(O=r.currentTrick)==null?void 0:O[0])==null?void 0:S.suit)||null,r.trumpSuit)});if(f.length>0){const p=f[0];return{strategy:"support",recommendedCard:p,reason:`🤝 SUPPORTO SICURO: Compagno imbattibile, non interferisco con ${p.rank}`}}}const I=n.filter(f=>C.suit===r.trumpSuit?f.suit===r.trumpSuit?t.getCardOrder(f)<=t.getCardOrder(C):!0:f.suit===r.trumpSuit?!1:f.suit===C.suit?t.getCardOrder(f)<=t.getCardOrder(C):!0);if(I.length===0)return{strategy:"support",recommendedCard:n.reduce((p,O)=>{const S=O.suit===r.trumpSuit?1e3+t.getCardOrder(O):t.getCardOrder(O),w=p.suit===r.trumpSuit?1e3+t.getCardOrder(p):t.getCardOrder(p);return S<w?O:p}),reason:"Giocando la carta meno competitiva per non danneggiare il compagno"};if(a&&(s>=1||g)){console.log(`[COLLABORATIVA AVANZATA] 🔥 ULTIMO GIOCATORE + COMPAGNO VINCENTE + PRESA PREZIOSA (${s.toFixed(1)} punti)`);const f=K(I);if(f.length>0){const S=f.reduce((w,F)=>t.getCardValue(F)>t.getCardValue(w)?F:w);return console.log(`[COLLABORATIVA AVANZATA] 🏆 VALORIZZAZIONE FIGURE: ${S.rank} di ${S.suit} (${t.getCardValue(S)} punti)`),{strategy:"support",recommendedCard:S,reason:`🏆 VALORIZZAZIONE FIGURE: Ultimo giocatore + team vincente, gioco ${S.rank} (${t.getCardValue(S)} punti)`}}const p=I.filter(S=>S.suit===r.trumpSuit&&(S.rank==="7"||S.rank==="6"||S.rank==="5"||S.rank==="4"));if(p.length>0&&s>=1){const S=p.filter(w=>{var F,B;return t.canWinCurrentTrick(w,r.currentTrick||[],((B=(F=r.currentTrick)==null?void 0:F[0])==null?void 0:B.suit)||null,r.trumpSuit)});if(S.length>0){S.sort((F,B)=>t.getCardStrengthScore(F)-t.getCardStrengthScore(B));const w=S[0];return console.log(`[COLLABORATIVA AVANZATA] 🚨 CORREZIONE CRITICA: Prendo con briscola bassa ${w.rank} di ${w.suit} per salvare ${s.toFixed(1)} punti!`),{strategy:"support",recommendedCard:w,reason:`🚨 SALVATAGGIO PUNTI: Ultimo giocatore, uso briscola bassa ${w.rank} per salvare ${s.toFixed(1)} punti del team!`}}}const O=I.filter(S=>_(S));if(O.length>0){O.sort((w,F)=>t.getCardValue(F)-t.getCardValue(w));const S=O[0];return console.log(`[COLLABORATIVA AVANZATA] 💰 FALLBACK PUNTI: ${S.rank} di ${S.suit} (${t.getCardValue(S)} punti)`),{strategy:"support",recommendedCard:S,reason:`💰 PUNTI FALLBACK: Ultimo giocatore, valorizzo con ${S.rank} (${t.getCardValue(S)} punti)`}}}if(s>=1){console.log(`[COLLABORATIVA AVANZATA] 🎯 NON-ULTIMO + COMPAGNO VINCENTE + PRESA PREZIOSA (${s.toFixed(1)} punti)`);const f=I.filter(p=>t.getCardValue(p)>0&&(p.rank==="A"||p.rank==="K"||p.rank==="H"||p.rank==="J"));if(f.length>0){f.sort((O,S)=>t.getCardValue(S)-t.getCardValue(O));const p=f[0];return console.log(`[COLLABORATIVA AVANZATA] 🏆 VALORIZZAZIONE NON-ULTIMO: ${p.rank} di ${p.suit} (${t.getCardValue(p)} punti)`),{strategy:"support",recommendedCard:p,reason:`🏆 VALORIZZAZIONE FIGURE: Compagno vincente + presa preziosa, gioco ${p.rank} (${t.getCardValue(p)} punti)`}}}return{strategy:"support",recommendedCard:I.reduce((f,p)=>{const O=Z(p,o,r),S=Z(f,o,r);return O<S?p:f}),reason:"Supportando il compagno con carta di scarto"}}return{strategy:"compete"}},cr={[G.EASY]:{errorRate:.4,cooperationRate:.6,strategicRate:.3,memoryUsage:.4},[G.MEDIUM]:{errorRate:.1,cooperationRate:.9,strategicRate:.6,memoryUsage:.7},[G.HARD]:{errorRate:0,cooperationRate:1,strategicRate:1,memoryUsage:1}},lr=(r,e,n,o)=>{var g,l;const t=cr[o],i=((l=(g=e.players)==null?void 0:g[n])==null?void 0:l.team)||"UNKNOWN",a=`CPU${n} (Team ${i}) [${o}]`;if(console.log(`
🤖 [UNIFIED AI] ${a} - Thinking...`),!e.currentTrick||e.currentTrick.length===0){const u=e.trickNumber??1;if(u===1&&e.trumpSuit&&e.lastTrumpSelector===n){const A=r.some(E=>E.suit===e.trumpSuit&&E.rank==="A"),h=r.some(E=>E.suit===e.trumpSuit&&E.rank==="2"),T=r.some(E=>E.suit===e.trumpSuit&&E.rank==="3");if(A&&h&&T){const E=r.find(N=>N.suit===e.trumpSuit&&N.rank==="A");if(E)return console.log(`[UNIFIED AI] 🎯🎯🎯 MARAFFA OBBLIGATORIA! Gioco Asso di ${E.suit} per 3 punti bonus!`),E}}const m=u<=3?.8:t.strategicRate;if(Math.random()<m){console.log(`[UNIFIED AI] 🎯 Tentativo apertura strategica (${(m*100).toFixed(0)}%)`);const A=r.filter(h=>h.rank==="3"&&h.suit!==e.trumpSuit);if(A.length>0){const h=A[0];return console.log(`[UNIFIED AI] ✅ Apertura con 3 di ${h.suit}`),h}}}if(Math.random()<t.cooperationRate){const u=se(e),d=ur(e,n,r,u);if(d.strategy==="support"&&d.recommendedCard)return console.log(`[UNIFIED AI] 🤝 SUPPORTO TEAM (${(t.cooperationRate*100).toFixed(0)}%): ${d.reason}`),d.recommendedCard;if(d.strategy==="compete"&&d.recommendedCard)if(!e.currentTrick||e.currentTrick.length===0){const h=new x().getCardValue(d.recommendedCard),T=["4","5","6","7"].includes(d.recommendedCard.rank),E=d.recommendedCard.suit===e.trumpSuit;if(h>0&&!T&&!E)console.log(`[UNIFIED AI] ⚠️ BLOCCO strategia cooperativa - carta con punti (${d.recommendedCard.rank} di ${d.recommendedCard.suit}) non appropriata per primo turno`);else return console.log(`[UNIFIED AI] 🚨 RECUPERO AGGRESSIVO (${(t.cooperationRate*100).toFixed(0)}%): ${d.reason}`),d.recommendedCard}else return console.log(`[UNIFIED AI] 🚨 RECUPERO AGGRESSIVO (${(t.cooperationRate*100).toFixed(0)}%): ${d.reason}`),d.recommendedCard}if(e.currentTrick&&e.currentTrick.length>0&&e.currentTrick.some(d=>d&&d.rank==="A")){console.log("[UNIFIED AI] 🔥 ASSO SUL TAVOLO! Cerco di prenderlo...");const d=new x,m=r.filter(A=>{const h=[...e.currentTrick.filter(Boolean),A];return d.canWinCurrentTrick(A,h,e.leadSuit,e.trumpSuit)});if(m.length>0)if(Math.random()>t.errorRate){const A=m[0];return console.log(`[UNIFIED AI] 🔥 PRENDO ASSO CON ${A.rank} di ${A.suit}!`),A}else console.log(`[UNIFIED AI] 😵 ERRORE: Non riesco a prendere l'asso (${(t.errorRate*100).toFixed(0)}% errore)`)}if(Math.random()<t.memoryUsage){console.log(`[UNIFIED AI] 🧠 Uso analisi memoria (${(t.memoryUsage*100).toFixed(0)}%)`);const u=or(r,e.currentTrick||[],e,n);if(u.optimalCards.length>0&&Math.random()>t.errorRate){const d=u.optimalCards[0];if((!e.currentTrick||e.currentTrick.length===0)&&d.suit===e.trumpSuit){const A=r.filter(h=>h.suit===e.trumpSuit);if(A.length<4)console.log(`[UNIFIED AI] ⚠️ Memoria suggerisce briscola ${d.rank}, ma ho solo ${A.length} briscole - ignoro suggerimento`);else return console.log(`[UNIFIED AI] ✅ Carta ottimale dalla memoria: ${d.rank} di ${d.suit}`),d}else return console.log(`[UNIFIED AI] ✅ Carta ottimale dalla memoria: ${d.rank} di ${d.suit}`),d}}if(Math.random()<t.errorRate){console.log(`[UNIFIED AI] 😵 ERRORE CASUALE (${(t.errorRate*100).toFixed(0)}% probabilità)`);const u=r[Math.floor(Math.random()*r.length)];return console.log(`[UNIFIED AI] 🎲 Gioco carta casuale: ${u.rank} di ${u.suit}`),u}if(console.log("[UNIFIED AI] 🎯 Logica ottimale"),!e.currentTrick||e.currentTrick.length===0){console.log("[UNIFIED AI] 🎯 PRIMO DEL TURNO - Strategia apertura intelligente");const u=new x,d=Re(e);r.filter(L=>L.suit===e.trumpSuit);const m=rr(r,e);let A=r;m.shouldConserve&&m.cardToAvoid&&(A=r.filter(L=>{var U,v;return!(L.suit===((U=m.cardToAvoid)==null?void 0:U.suit)&&L.rank===((v=m.cardToAvoid)==null?void 0:v.rank))}),console.log(`[UNIFIED AI] 🎯 ${m.reason}`));const h=A.filter(L=>{if((L.rank==="A"||L.rank==="K")&&L.suit!==e.trumpSuit)return!1;if(L.suit===e.trumpSuit){const U=r.filter(v=>v.suit===e.trumpSuit);if(U.length<4)return console.log(`[UNIFIED AI] ⚠️ Evito briscola ${L.rank} come apertura - ho solo ${U.length} briscole`),!1}return je(L,d,e.trumpSuit)});if(h.length>0){const L=nr(h,d);if(L.length>0){const U=L[0];return console.log(`[UNIFIED AI] ✅ CARTA SICURA: ${U.rank} di ${U.suit}`),U}}const T=er(A,e);if(T.shouldPlayTrump&&T.recommendedCard)return console.log(`[UNIFIED AI] 🎯 ${T.reason}`),T.recommendedCard;const E=A.filter(L=>{const U=u.getCardValue(L)===0,v=L.suit!==e.trumpSuit,b=["4","5","6","7"].includes(L.rank);return U&&v&&b});if(E.length>0){const L=E.reduce((U,v)=>u.getCardOrder(v)>u.getCardOrder(U)?v:U);return console.log(`[UNIFIED AI] ✅ CARTA LISCIA: ${L.rank} di ${L.suit} (nessun punto da perdere)`),L}const N=A.filter(L=>!((L.rank==="A"||L.rank==="K")&&L.suit!==e.trumpSuit)),y=(N.length>0?N:A).reduce((L,U)=>u.getCardValue(U)<u.getCardValue(L)?U:L);return console.log(`[UNIFIED AI] ⚠️ FALLBACK: ${y.rank} di ${y.suit} (minor rischio, evito assi/re non-briscola)`),y}const s=new x,c=r.reduce((u,d)=>s.getCardValue(d)<s.getCardValue(u)?d:u);return console.log(`[UNIFIED AI] 📉 Carta di minor valore: ${c.rank} di ${c.suit}`),c};class dr{constructor(){ee(this,"playedCards",[]);ee(this,"gameRound",0);this.playedCards=[]}startNewRound(){this.playedCards=[],this.gameRound++}recordPlayedCard(e,n){this.playedCards.push(e)}getPlayedCards(){return[...this.playedCards]}isCardPlayed(e){return this.playedCards.some(n=>n.suit===e.suit&&n.rank===e.rank)}updateMemory(e){}reset(){this.playedCards=[],this.gameRound=0}}const gr=new dr,mr=(r,e,n)=>{var c,g;const o=((g=(c=r.players)==null?void 0:c[e])==null?void 0:g.team)||"UNKNOWN",t=`CPU${e} (Team ${o}) [${n}]`;if(console.log(`
🤖 ===== ${t} STA PENSANDO... =====`),!r||!r.players||!r.players[e]||!r.players[e].hand)return console.log(`❌ ${t} - ERRORE: dati giocatore non validi`),null;let a=[...r.players[e].hand];if(!r.currentTrick||r.currentTrick.length===0){if((r.trickNumber??1)===1&&r.trumpSuit&&r.lastTrumpSelector===e){const d=a.some(h=>h.suit===r.trumpSuit&&h.rank==="A"),m=a.some(h=>h.suit===r.trumpSuit&&h.rank==="2"),A=a.some(h=>h.suit===r.trumpSuit&&h.rank==="3");if(d&&m&&A){const h=a.find(T=>T.suit===r.trumpSuit&&T.rank==="A");if(h)return console.log(`🎯🎯🎯 ${t} - MARAFFA OBBLIGATORIA! Gioco Asso di ${h.suit} per 3 punti bonus!`),h}}console.log(`🎯 ${t} - PRIMO DEL TURNO - CONTROLLO 3 NON-BRISCOLA!`);const u=a.filter(d=>d.rank==="3"&&d.suit!==r.trumpSuit);if(u.length>0){const d=u[0];return console.log(`🎯 ${t} - STRATEGIA APERTURA: GIOCO 3 di ${d.suit}!`),d}}if(r.leadSuit&&r.currentTrick&&r.currentTrick.length>0){const l=a.filter(u=>u.suit===r.leadSuit);l.length>0&&(a=l,console.log(`🎴 ${t} - Devo seguire il seme: ${r.leadSuit} (${l.length} carte)`))}if(a.length===0)return console.log(`❌ ${t} - ERRORE: nessuna carta disponibile`),null;if(r.currentTrick&&r.currentTrick.length>0&&r.currentTrick.some(u=>u&&u.rank==="A")){console.log(`🔥 ${t} - ASSO SUL TAVOLO! Cerco di prenderlo...`);const u=new x,d=a.filter(m=>{const A=[...r.currentTrick,m];return u.canWinCurrentTrick(m,A,r.leadSuit,r.trumpSuit)});if(d.length>0){const m=d[0];return console.log(`🔥 ${t} - PRENDO ASSO CON ${m.rank} di ${m.suit}!`),m}}try{gr.updateMemory(r)}catch(l){console.log(`⚠️ ${t} - Errore aggiornamento memoria:`,l)}console.log(`🎯 ${t} - STRATEGIA UNIFICATA [${n}]`);const s=lr(a,r,e,n);return console.log(`✅ ${t} - DECISIONE FINALE: ${(s==null?void 0:s.rank)||"NULL"} di ${(s==null?void 0:s.suit)||"NULL"}`),s},fr=(r,e,n)=>{if(e.currentTrick.length>0||e.trickNumber===1||e.trickNumber>5)return null;const o=n.suit,t=r.hand.filter(l=>l.suit===o),i=e.trumpSuit?r.hand.filter(l=>l.suit===e.trumpSuit):[];if(t.length===1&&i.length>0)return"volo";const a=t.some(l=>l.rank===k.Two),s=t.some(l=>l.rank===k.Three),c=t.some(l=>l.rank===k.Ace);return a&&n.suit===o&&n.rank!==k.Two&&!s&&!c&&t.length>=2?(console.log(`[BUSSO] 🎯 Gioco ${n.rank} di ${n.suit} - ho il 2 dello stesso seme!`),"busso"):t.length===2?"striscio":null},pr=(r,e,n,o)=>{const t=fr(r,e,n);if(!t)return null;const i={easy:.4,medium:.7,hard:.9};if(Math.random()>i[o])return null;switch(t){case"busso":return Ar(r,e,n)?"busso":null;case"volo":return Math.random()<.8?"volo":null;case"striscio":return Math.random()<.6?"striscio":null;default:return null}},Ar=(r,e,n)=>{if(!r.hand.filter(c=>c.suit===n.suit).some(c=>c.rank===k.Two)||e.trickNumber>5)return!1;const i=e.players.findIndex(c=>c.team===r.team&&c.id!==r.id);if(i===-1)return!1;const a=e.currentPlayer;return(i-a+4)%4<=2},hr=(r,e)=>{if(console.log(`[STRATEGIC ANNOUNCEMENTS] 📢 ${e.players[r.playerIndex].name} dichiara: ${r.type.toUpperCase()}`),typeof window<"u"&&window.aiMemory){const n=window.aiMemory;n.strategicAnnouncements||(n.strategicAnnouncements=[]),n.strategicAnnouncements.push({...r,timestamp:Date.now()});const o=e.trickNumber;n.strategicAnnouncements=n.strategicAnnouncements.filter(t=>o-t.trickNumber<=3)}},Cr=(r,e)=>{if(typeof window>"u"||!window.aiMemory)return[];const n=window.aiMemory;if(!n.strategicAnnouncements)return[];const o=r.trickNumber;return n.strategicAnnouncements.filter(t=>{if(o-t.trickNumber>2)return!1;const i=r.players[t.playerIndex],a=r.players[e];return i.team===a.team?!0:t.type==="volo"})},Tr=(r,e,n)=>{const o=Cr(e,n),t=e.players[n],i=o.filter(s=>e.players[s.playerIndex].team===t.team&&s.playerIndex!==n);if(i.length===0)return{recommendedCards:r,strategy:"Nessuna dichiarazione del compagno"};const a=i[i.length-1];switch(a.type){case"busso":return Or(r,e,a);case"striscio":return Sr(r,e,a);case"volo":return Ir(r,e,a);default:return{recommendedCards:r,strategy:"Dichiarazione non riconosciuta"}}},Or=(r,e,n)=>{const o=n.suit,t=r.filter(s=>s.suit===o);if(t.length>0){const c=t.sort((g,l)=>{const u=d=>({3:10,2:9,A:8,K:7,H:6,J:5,7:4,6:3,5:2,4:1})[d.rank]||0;return u(l)-u(g)})[0];return{recommendedCards:[c],strategy:`BUSSO: Prendo con ${c.rank} di ${o} (carta più forte del seme)`}}const i=r.filter(s=>s.suit===e.trumpSuit);if(i.length>0){const s=i.sort((c,g)=>{const l=u=>({3:10,2:9,A:8,K:7,H:6,J:5,7:4,6:3,5:2,4:1})[u.rank]||0;return l(c)-l(g)});return{recommendedCards:[s[0]],strategy:`BUSSO: Prendo con briscola ${s[0].rank} (non ho carte del seme ${o})`}}const a=r.filter(s=>s.suit===e.trumpSuit?!0:s.suit===o?s.rank===k.Three||s.rank===k.Two||s.rank===k.Ace:!1);return a.length>0?{recommendedCards:a,strategy:`BUSSO: Prendo la mano per collaborare con il compagno (seme: ${o})`}:{recommendedCards:r,strategy:"BUSSO: Non posso prendere la mano"}},Sr=(r,e,n)=>({recommendedCards:r,strategy:`STRISCIO: Il compagno ha ancora carte in ${n.suit}`}),Ir=(r,e,n)=>{const o=n.suit,t=r.filter(i=>i.suit===o);if(t.length>0&&e.leadSuit===o){const i=t.filter(a=>a.rank===k.Ace||a.rank===k.King||a.rank===k.Horse||a.rank===k.Jack);if(i.length>0)return{recommendedCards:i,strategy:`VOLO: Do punti al compagno che tagliarà (seme: ${o})`}}return{recommendedCards:r,strategy:`VOLO: Il compagno può tagliare ${o}`}},kr=(r,e,n,o)=>{if(r.currentTrick.length>0)return{shouldAnnounce:!1,announcement:null,reason:"Non è il primo del turno"};const t=r.players[e],i=pr(t,r,n,o);return i?(console.log(`[AI STRATEGIC] Player ${e} (${t.name}) considera dichiarazione: ${i}`),{shouldAnnounce:!0,announcement:i,reason:`Dichiarazione strategica: ${i} (${n.rank} di ${n.suit})`}):{shouldAnnounce:!1,announcement:null,reason:"Nessuna dichiarazione strategica appropriata"}},yr=(r,e,n)=>{const o=Tr(r,e,n);let t=.5;return o.strategy.includes("BUSSO")?t=.9:o.strategy.includes("VOLO")?t=.8:o.strategy.includes("STRISCIO")&&(t=.6),console.log(`[AI ANNOUNCEMENT STRATEGY] ${o.strategy} (Confidenza: ${(t*100).toFixed(0)}%)`),{filteredCards:o.recommendedCards,strategy:o.strategy,confidence:t}},Rr=(r,e,n,o)=>{if(!n)return;const t={type:n,playerIndex:e,suit:o,trickNumber:r.trickNumber};hr(t,r);const i=r.players[e];console.log(`[AI MEMORY UPDATE] Processed announcement: ${i.name} declares ${n.toUpperCase()} on ${o}`)},Er=(r,e,n,o)=>{if((!e.currentTrick||e.currentTrick.length===0)&&r.filter(s=>s.rank==="3"&&s.suit!==e.trumpSuit).length>0)return console.log("[STRATEGIC HANDLER] 🎯🎯🎯 PRIMO DEL TURNO CON 3 NON-BRISCOLA - BLOCCO FILTRI! 🎯🎯🎯"),{cards:r,strategicInfo:"PRIORITÀ ASSOLUTA: 3 non-briscola disponibile - no filtri strategici",useAnnouncement:!1};const i=yr(r,e,n);return i.confidence>=.7?{cards:i.filteredCards,strategicInfo:`Strategia basata su dichiarazione: ${i.strategy}`,useAnnouncement:!0}:{cards:r,strategicInfo:"Strategia normale - no dichiarazioni influenti",useAnnouncement:!1}},br=(r,e,n,o,t,i)=>{const a=r.players[r.currentPlayer];if(!a||!a.hand){console.error("Errore: giocatore corrente o mano non definiti");return}const s=De(a.hand,r.leadSuit);s.length>0&&c(r);function c(l){var h;const u=e==="easy"?G.EASY:e==="medium"?G.MEDIUM:G.HARD,d=Er(s,l,l.currentPlayer);console.log(`[AI STRATEGIC HANDLER] ${d.strategicInfo}`);const m=[...a.hand];if(d.useAnnouncement){const T=d.cards;a.hand=a.hand.filter(E=>T.some(N=>N.id===E.id)),console.log(`[AI STRATEGIC] Filtering hand from ${m.length} to ${a.hand.length} cards`)}const A=mr(l,l.currentPlayer,u);if(a.hand=m,A){if(l.currentTrick.length===0){const T=kr(l,l.currentPlayer,A,e);if(T.shouldAnnounce){console.log(`[AI ANNOUNCEMENT] ${a.name}: ${(h=T.announcement)==null?void 0:h.toUpperCase()}`),console.log(`[AI ANNOUNCEMENT] Ragione: ${T.reason}`);const E=ke(l,T.announcement);i&&(i({type:T.announcement,playerIndex:l.currentPlayer}),setTimeout(()=>{i({type:null,playerIndex:-1})},2500)),Rr(E,l.currentPlayer,T.announcement,A.suit),setTimeout(()=>{g(E,A)},1e3);return}}g(l,A)}else if(console.error("L'AI non è riuscita a scegliere una carta valida in modalità",e),s.length>0){re.playSound("cardSnap",{playerId:l.currentPlayer});const T=oe(l,s[0].id);o(s[0]),n(T)}}function g(l,u){re.playSound("cardPlay",{playerId:l.currentPlayer});const d=oe(l,u.id);o(u),d.currentTrick.length===0&&l.currentTrick.length===3?(setTimeout(()=>re.playSound("cardGather"),500),t(!0),setTimeout(()=>{t(!1),o(null),n(d)},2800)):n(d)}};export{q as C,k as R,M as S,Ae as W,ke as a,_e as b,Je as c,Ke as d,We as e,Nr as f,De as g,br as h,xe as i,Y as j,vr as k,Ie as l,Lr as m,Vr as n,oe as p,pe as r,X as s};
