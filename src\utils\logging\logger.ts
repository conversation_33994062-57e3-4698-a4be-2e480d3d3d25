/**
 * 🔍 Sistema di Logging Avanzato
 * <PERSON><PERSON> livelli, filtri, persistenza e formattazione
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  CRITICAL = 4,
}

interface LogEntry {
  timestamp: number;
  level: LogLevel;
  category: string;
  message: string;
  data?: any;
  stack?: string;
}

interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableStorage: boolean;
  maxStorageEntries: number;
  categories: string[];
}

class Logger {
  private config: LoggerConfig;
  private logs: LogEntry[] = [];
  private readonly STORAGE_KEY = "maraffa_logs";

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = {
      level: LogLevel.INFO,
      enableConsole: true,
      enableStorage: false,
      maxStorageEntries: 1000,
      categories: [],
      ...config,
    };

    // Carica log esistenti dal localStorage
    if (this.config.enableStorage) {
      this.loadFromStorage();
    }
  }

  /**
   * Log di debug (sviluppo)
   */
  debug(message: string, data?: any, category = "DEBUG"): void {
    this.log(LogLevel.DEBUG, category, message, data);
  }

  /**
   * Log informativo
   */
  info(message: string, data?: any, category = "INFO"): void {
    this.log(LogLevel.INFO, category, message, data);
  }

  /**
   * Log di avvertimento
   */
  warn(message: string, data?: any, category = "WARN"): void {
    this.log(LogLevel.WARN, category, message, data);
  }

  /**
   * Log di errore
   */
  error(message: string, error?: Error | any, category = "ERROR"): void {
    const stack = error instanceof Error ? error.stack : undefined;
    this.log(LogLevel.ERROR, category, message, error, stack);
  }

  /**
   * Log critico
   */
  critical(message: string, error?: Error | any, category = "CRITICAL"): void {
    const stack = error instanceof Error ? error.stack : undefined;
    this.log(LogLevel.CRITICAL, category, message, error, stack);
  }

  /**
   * Log generico
   */
  private log(level: LogLevel, category: string, message: string, data?: any, stack?: string): void {
    // Filtra per livello
    if (level < this.config.level) return;

    // Filtra per categoria
    if (this.config.categories.length > 0 && !this.config.categories.includes(category)) {
      return;
    }

    const entry: LogEntry = {
      timestamp: Date.now(),
      level,
      category,
      message,
      data,
      stack,
    };

    // Aggiungi ai log interni
    this.logs.push(entry);

    // Mantieni solo gli ultimi N log
    if (this.logs.length > this.config.maxStorageEntries) {
      this.logs = this.logs.slice(-this.config.maxStorageEntries);
    }

    // Console output
    if (this.config.enableConsole) {
      this.logToConsole(entry);
    }

    // Storage persistente
    if (this.config.enableStorage) {
      this.saveToStorage();
    }
  }

  /**
   * Output formattato su console
   */
  private logToConsole(entry: LogEntry): void {
    const timestamp = new Date(entry.timestamp).toISOString();
    const levelName = LogLevel[entry.level];
    const prefix = `[${timestamp}] [${levelName}] [${entry.category}]`;
    
    const style = this.getConsoleStyle(entry.level);
    
    if (entry.data || entry.stack) {
      console.groupCollapsed(`%c${prefix} ${entry.message}`, style);
      if (entry.data) {
        console.log("Data:", entry.data);
      }
      if (entry.stack) {
        console.log("Stack:", entry.stack);
      }
      console.groupEnd();
    } else {
      console.log(`%c${prefix} ${entry.message}`, style);
    }
  }

  /**
   * Stili console per livelli
   */
  private getConsoleStyle(level: LogLevel): string {
    switch (level) {
      case LogLevel.DEBUG:
        return "color: #6b7280; font-size: 11px;";
      case LogLevel.INFO:
        return "color: #3b82f6; font-weight: normal;";
      case LogLevel.WARN:
        return "color: #f59e0b; font-weight: bold;";
      case LogLevel.ERROR:
        return "color: #ef4444; font-weight: bold;";
      case LogLevel.CRITICAL:
        return "color: #dc2626; font-weight: bold; background: #fef2f2; padding: 2px 4px;";
      default:
        return "";
    }
  }

  /**
   * Salva log nel localStorage
   */
  private saveToStorage(): void {
    try {
      const recentLogs = this.logs.slice(-100); // Solo ultimi 100
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(recentLogs));
    } catch (error) {
      console.warn("Impossibile salvare log nel localStorage:", error);
    }
  }

  /**
   * Carica log dal localStorage
   */
  private loadFromStorage(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const parsedLogs = JSON.parse(stored) as LogEntry[];
        this.logs = parsedLogs.filter(log => 
          Date.now() - log.timestamp < 24 * 60 * 60 * 1000 // Ultimi 24h
        );
      }
    } catch (error) {
      console.warn("Impossibile caricare log dal localStorage:", error);
    }
  }

  /**
   * Ottieni tutti i log
   */
  getLogs(filter?: { level?: LogLevel; category?: string; since?: number }): LogEntry[] {
    let filtered = [...this.logs];

    if (filter?.level !== undefined) {
      filtered = filtered.filter(log => log.level >= filter.level!);
    }

    if (filter?.category) {
      filtered = filtered.filter(log => log.category === filter.category);
    }

    if (filter?.since) {
      filtered = filtered.filter(log => log.timestamp >= filter.since!);
    }

    return filtered.sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * Esporta log come testo
   */
  exportLogs(): string {
    return this.logs
      .map(entry => {
        const timestamp = new Date(entry.timestamp).toISOString();
        const level = LogLevel[entry.level];
        let line = `[${timestamp}] [${level}] [${entry.category}] ${entry.message}`;
        
        if (entry.data) {
          line += `\nData: ${JSON.stringify(entry.data, null, 2)}`;
        }
        
        if (entry.stack) {
          line += `\nStack: ${entry.stack}`;
        }
        
        return line;
      })
      .join("\n\n");
  }

  /**
   * Pulisci log
   */
  clearLogs(): void {
    this.logs = [];
    if (this.config.enableStorage) {
      localStorage.removeItem(this.STORAGE_KEY);
    }
  }

  /**
   * Aggiorna configurazione
   */
  updateConfig(newConfig: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Ottieni statistiche
   */
  getStats(): { total: number; byLevel: Record<string, number>; byCategory: Record<string, number> } {
    const byLevel: Record<string, number> = {};
    const byCategory: Record<string, number> = {};

    this.logs.forEach(log => {
      const levelName = LogLevel[log.level];
      byLevel[levelName] = (byLevel[levelName] || 0) + 1;
      byCategory[log.category] = (byCategory[log.category] || 0) + 1;
    });

    return {
      total: this.logs.length,
      byLevel,
      byCategory,
    };
  }
}

// Istanza globale del logger
export const logger = new Logger({
  level: process.env.NODE_ENV === "development" ? LogLevel.DEBUG : LogLevel.INFO,
  enableConsole: true,
  enableStorage: process.env.NODE_ENV === "development",
  categories: process.env.NODE_ENV === "development" ? [] : ["ERROR", "CRITICAL", "WARN"],
});

// Shortcut per categorie specifiche
export const gameLogger = {
  debug: (msg: string, data?: any) => logger.debug(msg, data, "GAME"),
  info: (msg: string, data?: any) => logger.info(msg, data, "GAME"),
  warn: (msg: string, data?: any) => logger.warn(msg, data, "GAME"),
  error: (msg: string, error?: any) => logger.error(msg, error, "GAME"),
};

export const aiLogger = {
  debug: (msg: string, data?: any) => logger.debug(msg, data, "AI"),
  info: (msg: string, data?: any) => logger.info(msg, data, "AI"),
  warn: (msg: string, data?: any) => logger.warn(msg, data, "AI"),
  error: (msg: string, error?: any) => logger.error(msg, error, "AI"),
};

export const uiLogger = {
  debug: (msg: string, data?: any) => logger.debug(msg, data, "UI"),
  info: (msg: string, data?: any) => logger.info(msg, data, "UI"),
  warn: (msg: string, data?: any) => logger.warn(msg, data, "UI"),
  error: (msg: string, error?: any) => logger.error(msg, error, "UI"),
};
