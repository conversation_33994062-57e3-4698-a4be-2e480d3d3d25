import { useState, useCallback, useRef, useEffect } from "react";
import { GameState, initializeGame } from "@/utils/game/gameLogic";
import { Card } from "@/utils/game/cardUtils";

interface UseGameStateOptions {
  difficulty?: "easy" | "medium" | "hard";
  onGameEnd?: (winner: number) => void;
  onError?: (error: Error) => void;
}

interface UseGameStateReturn {
  gameState: GameState;
  isLoading: boolean;
  error: string | null;
  actions: {
    initializeNewGame: () => void;
    playCard: (card: Card) => Promise<void>;
    selectTrump: (suit: string) => void;
    resetGame: () => void;
  };
}

/**
 * 🎮 Hook personalizzato per la gestione dello stato del gioco
 * Centralizza la logica di gioco e fornisce un'API pulita
 */
export const useGameState = (options: UseGameStateOptions = {}): UseGameStateReturn => {
  const { difficulty = "medium", onGameEnd, onError } = options;
  
  const [gameState, setGameState] = useState<GameState>(() => 
    initializeGame(difficulty)
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Ref per evitare stale closures
  const gameStateRef = useRef(gameState);
  gameStateRef.current = gameState;

  // Inizializza un nuovo gioco
  const initializeNewGame = useCallback(() => {
    try {
      setIsLoading(true);
      setError(null);
      
      const newGameState = initializeGame(difficulty);
      setGameState(newGameState);
      
      console.log("🎮 Nuovo gioco inizializzato:", {
        difficulty,
        players: newGameState.players.length,
        trumpSuit: newGameState.trumpSuit,
      });
    } catch (err) {
      const error = err instanceof Error ? err : new Error("Errore sconosciuto");
      setError(error.message);
      onError?.(error);
      console.error("❌ Errore inizializzazione gioco:", error);
    } finally {
      setIsLoading(false);
    }
  }, [difficulty, onError]);

  // Gioca una carta
  const playCard = useCallback(async (card: Card) => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Qui implementeresti la logica per giocare una carta
      // Per ora è un placeholder
      console.log("🃏 Giocando carta:", card);
      
      // Simula un delay per operazioni asincrone
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Aggiorna lo stato del gioco
      // setGameState(newState);
      
    } catch (err) {
      const error = err instanceof Error ? err : new Error("Errore nel giocare la carta");
      setError(error.message);
      onError?.(error);
      console.error("❌ Errore nel giocare carta:", error);
    } finally {
      setIsLoading(false);
    }
  }, [onError]);

  // Seleziona la briscola
  const selectTrump = useCallback((suit: string) => {
    try {
      setError(null);
      
      setGameState(prevState => ({
        ...prevState,
        trumpSuit: suit,
        gamePhase: "playing",
      }));
      
      console.log("🎯 Briscola selezionata:", suit);
    } catch (err) {
      const error = err instanceof Error ? err : new Error("Errore nella selezione briscola");
      setError(error.message);
      onError?.(error);
      console.error("❌ Errore selezione briscola:", error);
    }
  }, [onError]);

  // Reset del gioco
  const resetGame = useCallback(() => {
    try {
      setError(null);
      initializeNewGame();
    } catch (err) {
      const error = err instanceof Error ? err : new Error("Errore nel reset del gioco");
      setError(error.message);
      onError?.(error);
      console.error("❌ Errore reset gioco:", error);
    }
  }, [initializeNewGame, onError]);

  // Controlla se il gioco è finito
  useEffect(() => {
    if (gameState.gamePhase === "gameOver" && gameState.winner !== undefined) {
      onGameEnd?.(gameState.winner);
    }
  }, [gameState.gamePhase, gameState.winner, onGameEnd]);

  return {
    gameState,
    isLoading,
    error,
    actions: {
      initializeNewGame,
      playCard,
      selectTrump,
      resetGame,
    },
  };
};
