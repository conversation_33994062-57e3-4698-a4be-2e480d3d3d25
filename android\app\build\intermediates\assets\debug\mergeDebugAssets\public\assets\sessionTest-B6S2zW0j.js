const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-SQuFrxH6.js","assets/ui-CVF02LcF.js","assets/vendor-COrNHRvO.js","assets/audio-5UOY9KLB.js","assets/supabase-DLIhrfaA.js","assets/game-DyVU82mC.js","assets/audioManager-oMWoQbcF.js","assets/index-aBxC3Tw5.css"])))=>i.map(i=>d[i]);
import{_ as c}from"./supabase-DLIhrfaA.js";import{s as i}from"./index-SQuFrxH6.js";import"./vendor-COrNHRvO.js";import"./ui-CVF02LcF.js";import"./audio-5UOY9KLB.js";import"./game-DyVU82mC.js";import"./audioManager-oMWoQbcF.js";const l=async()=>{const e=[],o=Date.now();try{const s=i!==null&&i!==void 0;e.push({name:"SessionManager Initialization",success:s,message:s?"SessionManager inizializzato correttamente":"SessionManager non inizializzato",duration:Date.now()-o})}catch(s){e.push({name:"SessionManager Initialization",success:!1,message:`Errore inizializzazione: ${s}`,duration:Date.now()-o})}const r=Date.now();try{const n=await i.ensureValidSession()!==null;e.push({name:"Ensure Valid Session",success:!0,message:n?"Sessione valida ottenuta":"Nessuna sessione (normale se non loggato)",duration:Date.now()-r})}catch(s){e.push({name:"Ensure Valid Session",success:!1,message:`Errore verifica sessione: ${s}`,duration:Date.now()-r})}const t=Date.now();try{if(await i.ensureValidSession()){const a=await i.refreshSession()!==null;e.push({name:"Session Refresh",success:a,message:a?"Refresh sessione riuscito":"Refresh sessione fallito",duration:Date.now()-t})}else e.push({name:"Session Refresh",success:!0,message:"Skip test refresh - nessuna sessione attiva",duration:Date.now()-t})}catch(s){e.push({name:"Session Refresh",success:!1,message:`Errore refresh: ${s}`,duration:Date.now()-t})}return e},m=async()=>{const e=[],o=Date.now();try{const{retryWithRefresh:t}=await c(async()=>{const{retryWithRefresh:n}=await import("./index-SQuFrxH6.js").then(a=>a.c);return{retryWithRefresh:n}},__vite__mapDeps([0,1,2,3,4,5,6,7])),s=typeof t=="function";e.push({name:"Retry System Import",success:s,message:s?"Sistema retry importato correttamente":"Sistema retry non è una funzione",duration:Date.now()-o})}catch(t){e.push({name:"Retry System Import",success:!1,message:`Errore import retry system: ${t}`,duration:Date.now()-o})}const r=Date.now();try{const{retryWithRefresh:t}=await c(async()=>{const{retryWithRefresh:a}=await import("./index-SQuFrxH6.js").then(u=>u.c);return{retryWithRefresh:a}},__vite__mapDeps([0,1,2,3,4,5,6,7])),s=await t(async()=>({test:"success"})),n=s&&s.test==="success";e.push({name:"Simple Retry Operation",success:n,message:n?"Operazione retry semplice riuscita":"Operazione retry semplice fallita",duration:Date.now()-r})}catch(t){e.push({name:"Simple Retry Operation",success:!1,message:`Errore operazione retry: ${t}`,duration:Date.now()-r})}return e},d=async()=>{console.log("🧪 Inizio test sistema sessione...");const e=await l(),o=await m(),r=[...e,...o];console.log("📊 Risultati test sessione:"),r.forEach(n=>{const a=n.success?"✅":"❌";console.log(`${a} ${n.name}: ${n.message} (${n.duration}ms)`)});const t=r.filter(n=>n.success).length,s=r.length;console.log(`🎯 Test completati: ${t}/${s} riusciti`),t===s?console.log("🎉 Tutti i test sono passati! Sistema sessione funzionante."):console.warn("⚠️ Alcuni test sono falliti. Controllare la configurazione.")},z=async()=>{try{console.log("🧪 Quick test sessione - inizio...");const e=Date.now(),o=await i.ensureValidSession(),r=Date.now()-e;if(console.log(`🔍 Quick test sessione completato in ${r}ms:`,o?"✅ OK":"⚠️ Nessuna sessione"),o){const t=o.expires_at?o.expires_at*1e3:0,s=Date.now(),n=Math.round((t-s)/6e4);console.log(`⏰ Token scade tra ${n} minuti`)}return!0}catch(e){return console.error("❌ Quick test fallito:",e),!1}};export{z as quickSessionTest,d as runAllSessionTests,m as testApiCallsWithRetry,l as testSessionManager};
