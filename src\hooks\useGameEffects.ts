import React, { useEffect, useRef } from "react";
import { Card as CardType, getValidCards } from "@/utils/game/cardUtils";
import { GameState, startNewRound } from "@/utils/game/gameLogic";
import { handleAIMove } from "@/utils/ai/aiHandler";
import { handleTrumpSelection } from "@/utils/game/trumpHandler";
import { audioManager } from "@/utils/audio/AudioManager";
import {
  ActionAnnouncementType,
  TrumpAnnouncementType,
} from "@/types/gameTypes";

interface UseGameEffectsProps {
  gameState: GameState;
  difficulty: "easy" | "medium" | "hard";
  showingLastTrick: boolean;
  setGameState: React.Dispatch<React.SetStateAction<GameState>>;
  setValidCards: React.Dispatch<React.SetStateAction<CardType[]>>;
  setLastPlayedCard: React.Dispatch<React.SetStateAction<CardType | null>>;
  setShowingLastTrick: React.Dispatch<React.SetStateAction<boolean>>;
  setTrumpAnnouncement: React.Dispatch<
    React.SetStateAction<TrumpAnnouncementType>
  >;
  setActionAnnouncement: React.Dispatch<
    React.SetStateAction<ActionAnnouncementType>
  >;
  setCardHasBeenPlayed: React.Dispatch<React.SetStateAction<boolean>>;
  setShowRoundSummary: React.Dispatch<React.SetStateAction<boolean>>;
  setShowGameOverModal: React.Dispatch<React.SetStateAction<boolean>>;
  setCountdown: React.Dispatch<React.SetStateAction<number>>;
}

export const useGameEffects = ({
  gameState,
  difficulty,
  showingLastTrick,
  setGameState,
  setValidCards,
  setLastPlayedCard,
  setShowingLastTrick,
  setTrumpAnnouncement,
  setActionAnnouncement,
  setCardHasBeenPlayed,
  setShowRoundSummary,
  setShowGameOverModal,
  setCountdown,
}: UseGameEffectsProps) => {
  // Ref per tenere traccia dell'intervallo del countdown
  const countdownIntervalRef = useRef<NodeJS.Timeout | null>(null); // Funzione per saltare il countdown e iniziare immediatamente il nuovo round
  // MANTIENE LA STESSA SEQUENZA AUDIO: prima nasconde la modale, poi card-fan, poi nuovo round
  const skipCountdown = () => {
    if (countdownIntervalRef.current) {
      clearInterval(countdownIntervalRef.current);
      countdownIntervalRef.current = null;
    } // Prima nascondi la modale
    setShowRoundSummary(false);

    // Inizia nuovo round automaticamente
    setTimeout(() => {
      setGameState((prev) => {
        const newState = startNewRound(prev);
        audioManager.playSound("cardFan"); // Suono SOLO quando vengono date le carte
        return newState;
      });
    }, 100); // 100ms di delay per garantire che la modale sia scomparsa
  };
  // Effetto per aggiornare le carte valide quando cambia il giocatore corrente
  useEffect(() => {
    if (gameState.gamePhase === "play") {
      const currentPlayer = gameState.players[gameState.currentPlayer];
      const validCards = getValidCards(currentPlayer.hand, gameState.leadSuit);
      setValidCards(validCards);
    }
  }, [
    gameState.currentPlayer,
    gameState.leadSuit,
    gameState.gamePhase,
    gameState.players,
    setValidCards,
  ]);
  // Effetto per gestire le mosse dell'AI e la selezione briscola
  useEffect(() => {
    // Gestione mosse AI durante il gioco
    if (
      gameState.gamePhase === "play" &&
      gameState.currentPlayer !== 0 &&
      !showingLastTrick
    ) {
      const timeoutId = setTimeout(() => {
        handleAIMove(
          gameState,
          difficulty,
          setGameState,
          setLastPlayedCard,
          setShowingLastTrick,
          setActionAnnouncement
        );
      }, 1000);
      return () => clearTimeout(timeoutId);
    }

    // Gestione selezione briscola
    if (gameState.gamePhase === "selectTrump") {
      // 🎯 PRIORITÀ: Gestisci Maraffa automatica per QUALSIASI giocatore (umano o AI)
      if (
        gameState.automaticMaraffa?.hasMaraffa &&
        gameState.automaticMaraffa.playerIndex === gameState.currentPlayer
      ) {
        const timeoutId = setTimeout(() => {
          const playerName = gameState.players[gameState.currentPlayer].name;
          const maraffaSuit = gameState.automaticMaraffa.maraffaSuit;

          // Mostra l'annuncio della briscola selezionata automaticamente
          setTrumpAnnouncement({
            suit: maraffaSuit,
            playerName: playerName,
            visible: true,
          });

          // 🔊 Play maraffa sound for AI players
          playSound("maraffa");

          // NON mostrare il badge "Maraffa!" qui - verrà mostrato quando l'asso viene giocato

          setTimeout(() => {
            setTrumpAnnouncement((prev) => ({ ...prev, visible: false }));

            // Applica automaticamente la selezione della briscola
            import("../utils/game/gameLogic").then(
              ({ applyAutomaticMaraffa }) => {
                setGameState(applyAutomaticMaraffa(gameState));
              }
            );
          }, 2500);
        }, 1000);

        return () => clearTimeout(timeoutId);
      }

      // Gestione normale per AI senza Maraffa automatica
      if (gameState.currentPlayer !== 0) {
        const timeoutId = setTimeout(() => {
          handleTrumpSelection(
            gameState,
            difficulty,
            setTrumpAnnouncement,
            setGameState
          );
        }, 1500);
        return () => clearTimeout(timeoutId);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    gameState.gamePhase,
    gameState.currentPlayer,
    gameState.automaticMaraffa,
    gameState.trickNumber,
    difficulty,
    showingLastTrick,
  ]); // Effetto per gestire la modale di fine round
  useEffect(() => {
    // Mostra la modale di riepilogo quando un round è finito
    if (gameState.gamePhase === "roundOver") {
      // Delay ridotto a 1,5 secondo per permettere alle animazioni di completarsi
      setTimeout(() => {
        setShowRoundSummary(true);

        // 🔊 Suono card-shuffle SOLO quando compare effettivamente la modale di fine round
        audioManager.playSound("cardShuffleHandEnd");

        // Avvia il countdown per il nuovo round
        let secondsLeft = 6; // Ridotto da 8 a 6 secondi (riduzione di 2s)
        setCountdown(secondsLeft);

        const countdownInterval = setInterval(() => {
          secondsLeft -= 1;
          setCountdown(secondsLeft);

          if (secondsLeft <= 0) {
            clearInterval(countdownInterval);
            countdownIntervalRef.current = null;
            // Prima nascondi la modale
            setShowRoundSummary(false); // Inizia nuovo round automaticamente
            setTimeout(() => {
              setGameState((prev) => {
                const newState = startNewRound(prev);
                audioManager.playSound("cardFan"); // Suono SOLO quando vengono date le carte
                return newState;
              });
            }, 300);
          }
        }, 1000);

        countdownIntervalRef.current = countdownInterval;
      }, 1500); // Delay ridotto a 1,5 secondo
    }
  }, [gameState.gamePhase, setCountdown, setShowRoundSummary, setGameState]); // Effetto per gestire la modale di fine partita
  useEffect(() => {
    // Mostra la modale di fine partita quando il gioco è finito
    if (gameState.gamePhase === "gameOver") {
      console.log(
        "🎯 useGameEffects: Partita finita, apertura GameOverModal tra 2 secondi..."
      );
      // Delay fisso di 2 secondi per permettere alle animazioni di completarsi
      setTimeout(() => {
        console.log("🎯 useGameEffects: Apertura GameOverModal");
        setShowGameOverModal(true);
      }, 2000); // Delay fisso di 2 secondi
    } else {
      // Nascondi la modale se non siamo in gameOver
      setShowGameOverModal(false);
    }
  }, [gameState.gamePhase, setShowGameOverModal]);
  // Effetto per la pulizia quando il componente viene smontato
  useEffect(() => {
    return () => {
      // Cleanup any active timeouts or event listeners
      setShowingLastTrick(false);
      setLastPlayedCard(null);
      setCardHasBeenPlayed(false);
      setActionAnnouncement({ type: null, playerIndex: -1 });
      setTrumpAnnouncement({ suit: null, playerName: "", visible: false });

      // Pulisci anche l'intervallo del countdown
      if (countdownIntervalRef.current) {
        clearInterval(countdownIntervalRef.current);
        countdownIntervalRef.current = null;
      }
    };
  }, [
    setShowingLastTrick,
    setLastPlayedCard,
    setCardHasBeenPlayed,
    setActionAnnouncement,
    setTrumpAnnouncement,
  ]);

  // Restituisci la funzione skipCountdown per permettere di saltare il countdown
  return { skipCountdown };
};

// SEQUENZA AUDIO PER FINE TURNO/INIZIO NUOVA MANO:
// 1. Quando finisce un round (gamePhase === "roundOver"):
//    → Mostra modale di riepilogo + suono "card-shuffle" (mescolamento carte)
// 2. Quando la modale scompare (countdown finito o saltato manualmente):
//    → Nasconde modale + suono "card-fan" (distribuzione nuove carte) + inizia nuovo round
