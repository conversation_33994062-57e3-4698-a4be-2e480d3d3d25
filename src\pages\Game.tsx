import { useLocation } from "react-router-dom";
import { useState, useRef, useEffect } from "react";
import { useAudio } from "@/hooks/useAudio";
import { Card as CardType } from "@/utils/game/cardUtils";
import { GameState, initializeGameState } from "@/utils/game/gameLogic";
import { useIsMobile } from "@/hooks/use-mobile";
import { useAuth } from "@/context/auth-context";
import { useGameHandlers } from "@/hooks/useGameHandlers";
import { useGameEffects } from "@/hooks/useGameEffects";
import { positions } from "@/utils/game/gameStateHelpers";
import {
  ActionAnnouncementType,
  TrumpAnnouncementType,
} from "@/types/gameTypes";
import {
  isCanDeclareVictory,
  getTrumpSuitName,
  getTrumpSuitImage,
  getPlayedCardPosition,
  getPlayerTeam,
  getCurrentTeamPoints,
} from "@/utils/game/gameStateHelpers";
import { useSimpleImageCache } from "@/hooks/useSimpleImageCache";
import ImagePreloader from "@/components/ImagePreloader";
import { showDebugWarning, isDebugModeEnabled } from "@/utils/debug/debugMode";

// Components
import GameMenu from "@/components/GameMenu";
import GameScores from "@/components/GameScores";
import RoundSummary from "@/components/RoundSummary";
import PlayerHands from "@/components/PlayerHands";
import GameBoard from "@/components/GameBoard";
import GameControls from "@/components/GameControls";
import GameOverModal from "@/components/GameOverModal";
import TrumpSelectionModal from "@/components/TrumpSelectionModal";
import DebugPlayerHands from "@/components/DebugPlayerHands";
import AceBonusTooltip from "@/components/AceBonusTooltip";

const Game = () => {
  const location = useLocation();
  const {
    difficulty = "easy",
    isOnline = false,
    victoryPoints = 31,
  } = location.state || {};
  const isMobile = useIsMobile();
  const { playMusic, stopMusic, isTrackPlaying } = useAudio();

  // Game state
  const [gameState, setGameState] = useState<GameState>(
    initializeGameState(victoryPoints)
  );
  const [validCards, setValidCards] = useState<CardType[]>([]);
  const [showingLastTrick, setShowingLastTrick] = useState(false);
  const [lastPlayedCard, setLastPlayedCard] = useState<CardType | null>(null);
  const [showExitDialog, setShowExitDialog] = useState(false);
  const [showHelpSheet, setShowHelpSheet] = useState(false);
  const [cardHasBeenPlayed, setCardHasBeenPlayed] = useState(false);
  const [showRoundSummary, setShowRoundSummary] = useState(false);
  const [showGameOverModal, setShowGameOverModal] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [showDebugModal, setShowDebugModal] = useState(false);

  // Stato per tooltip Ace Bonus
  const [showAceBonusTooltip, setShowAceBonusTooltip] = useState(false);
  const [aceCardPosition, setAceCardPosition] = useState<
    { x: number; y: number } | undefined
  >();

  // Aggiungi classe al body per disabilitare scroll globale
  useEffect(() => {
    document.body.classList.add("game-active");
    document.documentElement.classList.add("game-active");

    return () => {
      document.body.classList.remove("game-active");
      document.documentElement.classList.remove("game-active");
    };
  }, []);

  // Announcement states
  const [actionAnnouncement, setActionAnnouncement] =
    useState<ActionAnnouncementType>({ type: null, playerIndex: -1 });
  const [trumpAnnouncement, setTrumpAnnouncement] =
    useState<TrumpAnnouncementType>({
      suit: null,
      playerName: "",
      visible: false,
    });

  // Refs and auth
  const gameStateRef = useRef(gameState);
  const { user } = useAuth();

  // Sistema di cache semplificato
  const { clearCache, isComplete } = useSimpleImageCache({
    autoPreload: true,
    debug: process.env.NODE_ENV === "development",
  });

  useEffect(() => {
    gameStateRef.current = gameState;
  }, [gameState]);

  // 🔍 Debug Mode Warning
  useEffect(() => {
    showDebugWarning();
  }, []);

  // Player position helper
  const getPlayerPosition = (playerIndex: number) => {
    return positions[playerIndex] as "bottom" | "left" | "top" | "right";
  };

  // Game handlers
  const {
    handleSelectTrump,
    handleAnnounceAction,
    handlePlayCard,
    handleDeclareWin,
    handleStartNewRound,
    handleStartNewGame,
    handleOpenMenu,
    handleReturnToMenu,
  } = useGameHandlers({
    gameState,
    setGameState,
    setValidCards,
    setShowingLastTrick,
    setLastPlayedCard,
    setShowExitDialog,
    setShowHelpSheet,
    setCardHasBeenPlayed,
    setActionAnnouncement,
    setTrumpAnnouncement,
    setShowRoundSummary,
    cardHasBeenPlayed,
    difficulty, // 🎯 NUOVO: Passa la difficulty per tracking abbandoni
  });
  // Game effects
  const { skipCountdown } = useGameEffects({
    gameState,
    difficulty,
    showingLastTrick,
    setGameState,
    setValidCards,
    setLastPlayedCard,
    setShowingLastTrick,
    setTrumpAnnouncement,
    setActionAnnouncement,
    setCardHasBeenPlayed,
    setShowRoundSummary,
    setShowGameOverModal,
    setCountdown,
  });

  // Prepare display cards
  const displayCards = [...gameState.currentTrick];
  if (
    lastPlayedCard &&
    !displayCards.find((card) => card.id === lastPlayedCard.id)
  ) {
    displayCards.push(lastPlayedCard);
  }

  // Helpers for UI state
  const getPlayerActionBadge = (playerIndex: number) => {
    return actionAnnouncement.playerIndex === playerIndex
      ? { type: actionAnnouncement.type, active: true }
      : { type: null, active: false };
  };

  const isDesktopLayout = !isMobile;
  const currentPoints = getCurrentTeamPoints(gameState);
  // Helper for played card positions
  const getPlayedCardPositionWithGameState = (trickIndex: number) => {
    return getPlayedCardPosition(trickIndex, gameState, isDesktopLayout);
  };

  // Helper for player team
  const getPlayerTeamWithGameState = (index: number) => {
    return getPlayerTeam(index, gameState);
  };

  // Funzione per trovare la posizione dell'Asso con badge M
  const findAceCardPosition = useCallback(() => {
    // Cerca l'elemento carta dell'Asso di briscola con badge M
    const aceCard = document.querySelector(
      `[data-card-rank="A"][data-card-suit="${gameState.trumpSuit}"][data-card-trump="false"]`
    );

    if (aceCard) {
      const rect = aceCard.getBoundingClientRect();
      return {
        x: rect.left + rect.width / 2,
        y: rect.top,
      };
    }

    // Fallback: posizione approssimativa al centro-basso dello schermo
    return {
      x: window.innerWidth / 2,
      y: window.innerHeight * 0.7,
    };
  }, [gameState.trumpSuit]);

  // Gestione tooltip Ace Bonus per Maraffa
  useEffect(() => {
    // Reset del tooltip quando cambia il turno o il giocatore
    if (gameState.currentPlayer !== 0 || gameState.currentTrick.length > 0) {
      if (showAceBonusTooltip) {
        setShowAceBonusTooltip(false);
      }
      return;
    }

    // Mostra tooltip solo se:
    // 1. È il turno del giocatore umano (index 0)
    // 2. Il giocatore ha la maraffa completa
    // 3. È la fase di gioco (non selezione briscola)
    // 4. È il primo giocatore del turno (nessuna carta ancora giocata)
    // 5. Il tooltip non è già mostrato per questo turno
    if (
      gameState.currentPlayer === 0 && // Giocatore umano
      gameState.gamePhase === "playing" && // Fase di gioco
      gameState.trumpSuit && // Briscola selezionata
      gameState.currentTrick.length === 0 && // Prima carta del turno
      !showAceBonusTooltip // Non già mostrato per questo turno
    ) {
      // Controlla se il giocatore ha la maraffa completa
      const playerHand = gameState.players[0].hand;
      const hasAce = playerHand.some(
        (card) => card.suit === gameState.trumpSuit && card.rank === "A"
      );
      const hasTwo = playerHand.some(
        (card) => card.suit === gameState.trumpSuit && card.rank === "2"
      );
      const hasThree = playerHand.some(
        (card) => card.suit === gameState.trumpSuit && card.rank === "3"
      );

      // Mostra tooltip se ha la maraffa completa (A, 2, 3 di briscola)
      if (hasAce && hasTwo && hasThree) {
        console.log(
          "🎯 DEBUG: Mostrando tooltip Ace Bonus - giocatore ha maraffa completa (A, 2, 3 di briscola)"
        );
        // Mostra il tooltip dopo un breve delay per permettere il rendering delle carte
        setTimeout(() => {
          const acePosition = findAceCardPosition();
          setAceCardPosition(acePosition);
          setShowAceBonusTooltip(true);
        }, 1500); // Aumentato delay per assicurarsi che le carte siano renderizzate
      } else {
        console.log("🎯 DEBUG: Maraffa incompleta - non mostro tooltip", {
          hasAce,
          hasTwo,
          hasThree,
          trumpSuit: gameState.trumpSuit,
        });
      }
    }
  }, [
    gameState.currentPlayer,
    gameState.trickNumber,
    gameState.gamePhase,
    gameState.trumpSuit,
    gameState.currentTrick.length,
    showAceBonusTooltip,
    gameState.players[0].hand, // Aggiungiamo la mano del giocatore alle dipendenze
    findAceCardPosition, // Aggiungiamo la funzione alle dipendenze
  ]);

  // Gestione badge Maraffa completata
  useEffect(() => {
    if (gameState.maraffaCompleted) {
      const timeoutId = setTimeout(() => {
        // Mostra il badge "Maraffa!" quando viene completata
        setActionAnnouncement({
          type: "maraffa",
          playerIndex: gameState.maraffaCompleted.playerIndex,
        });

        // Nascondi il badge dopo 3 secondi
        setTimeout(() => {
          setActionAnnouncement({ type: null, playerIndex: -1 });
        }, 3000);

        // Rimuovi il flag dal gameState per evitare che si ripeta
        setGameState((prevState) => ({
          ...prevState,
          maraffaCompleted: undefined,
        }));
      }, 500); // Breve delay per permettere all'animazione della carta di completarsi

      return () => clearTimeout(timeoutId);
    }
  }, [gameState.maraffaCompleted, setActionAnnouncement, setGameState]);
  return (
    <div className=" flex flex-col game-page">
      <div
        className="flex justify-between items-start mb-2"
        style={{ paddingTop: `calc(env(safe-area-inset-top, 0px))` }}
      >
        {" "}
        <GameMenu
          showHelpSheet={showHelpSheet}
          setShowHelpSheet={setShowHelpSheet}
          handleOpenMenu={handleOpenMenu}
          handleReturnToMenu={handleReturnToMenu}
          gameState={gameState}
          difficulty={difficulty}
          getTrumpSuitImage={() => getTrumpSuitImage(gameState.trumpSuit)}
          getTrumpSuitName={() => getTrumpSuitName(gameState.trumpSuit)}
          isMobile={isMobile}
          showDebugModal={showDebugModal}
          setShowDebugModal={setShowDebugModal}
        />{" "}
        <div className="flex flex-col items-end space-y-2">
          {/* 🔍 Debug Mode Indicator */}

          <GameScores
            gameState={gameState}
            currentPoints={currentPoints}
            isMobile={isMobile}
            isOnlineMode={isOnline}
          />

          {/* Contenitore per i controlli di gioco in colonna */}
          <div className="z-10">
            <GameControls
              phase={gameState.gamePhase}
              isCurrentPlayerTurn={gameState.currentPlayer === 0}
              onSelectTrump={handleSelectTrump}
              onAnnounceAction={handleAnnounceAction}
              onDeclareWin={handleDeclareWin}
              onStartNewRound={handleStartNewRound}
              onStartNewGame={handleStartNewGame}
              onReturnToMenu={handleReturnToMenu}
              message=""
              currentPlayerTeam={gameState.players[0].team}
              isTrickStart={gameState.currentTrick.length === 0}
              gameScore={gameState.gameScore}
              roundScore={gameState.roundScore}
              isMobile={isMobile}
              canDeclareVictory={isCanDeclareVictory(gameState)}
              isOnlineMode={isOnline}
            />
          </div>
        </div>
      </div>
      <GameBoard
        gameState={gameState}
        displayCards={displayCards}
        lastPlayedCard={lastPlayedCard}
        getPlayedCardPosition={getPlayedCardPositionWithGameState}
        getPlayerTeam={getPlayerTeamWithGameState}
        trumpAnnouncement={trumpAnnouncement}
        actionAnnouncement={actionAnnouncement}
        getTrumpSuitImage={() => getTrumpSuitImage(gameState.trumpSuit)}
        getTrumpSuitName={() => getTrumpSuitName(gameState.trumpSuit)}
        isMobile={isMobile}
        isDesktopLayout={isDesktopLayout}
        showingLastTrick={showingLastTrick}
      />{" "}
      <PlayerHands
        gameState={gameState}
        validCards={validCards}
        handlePlayCard={handlePlayCard}
        getPlayerPosition={getPlayerPosition}
        getPlayerActionBadge={getPlayerActionBadge}
        isMobile={isMobile}
        cardSize={isDesktopLayout ? "w-14 h-20" : "w-20 h-28"} // Dimensione ridotta in modalità desktop
        handCardSpacing={""}
      />{" "}
      {/* Componente Debug per mostrare le carte degli AI */}
      <DebugPlayerHands
        players={gameState.players}
        trumpSuit={gameState.trumpSuit}
        isVisible={showDebugModal}
        onToggleVisibility={() => setShowDebugModal(!showDebugModal)}
      />
      {/* Aggiungi il componente RoundSummary */}
      <RoundSummary
        visible={showRoundSummary}
        roundScore={gameState.roundScore}
        totalScore={gameState.gameScore}
        onContinue={handleStartNewRound}
        onSkip={skipCountdown}
        countdown={countdown}
      />{" "}
      {/* Modale fine partita */}
      <GameOverModal
        isOpen={showGameOverModal}
        gameState={gameState}
        onStartNewGame={handleStartNewGame}
        onReturnToMenu={handleReturnToMenu}
        isOnlineMode={isOnline}
        difficulty={difficulty}
      />{" "}
      {/* Modale di selezione della briscola */}
      <TrumpSelectionModal
        isVisible={
          gameState.gamePhase === "selectTrump" &&
          gameState.currentPlayer === 0 &&
          !gameState.automaticMaraffa?.hasMaraffa
        }
        onSelectTrump={handleSelectTrump}
      />
      {/* Precaricamento delle immagini */}
      <ImagePreloader autoStart={true} />
      {/* Tooltip Ace Bonus per Maraffa */}
      <AceBonusTooltip
        isVisible={showAceBonusTooltip}
        onClose={() => setShowAceBonusTooltip(false)}
        aceCardPosition={aceCardPosition}
      />
    </div>
  );
};

export default Game;
