import React, { useState, useEffect } from "react";
import { X, Bug, Database, Cpu, Eye, Download } from "lucide-react";
import { logger, gameLogger, aiLogger } from "@/utils/logging/logger";
import { imageCache, apiCache } from "@/utils/cache/advancedCache";

interface DebugPanelProps {
  isOpen: boolean;
  onClose: () => void;
  gameState?: any;
}

/**
 * 🐛 Pannello di Debug per Sviluppo
 * Mostra statistiche, log, cache e stato del gioco
 */
const DebugPanel: React.FC<DebugPanelProps> = ({ isOpen, onClose, gameState }) => {
  const [activeTab, setActiveTab] = useState<"logs" | "cache" | "game" | "performance">("logs");
  const [logs, setLogs] = useState<any[]>([]);
  const [cacheStats, setCacheStats] = useState<any>({});
  const [performanceMetrics, setPerformanceMetrics] = useState<any>({});

  // Aggiorna dati ogni secondo quando il pannello è aperto
  useEffect(() => {
    if (!isOpen) return;

    const updateData = () => {
      setLogs(logger.getLogs().slice(0, 50)); // Ultimi 50 log
      setCacheStats({
        images: imageCache.getStats(),
        api: apiCache.getStats(),
      });
      
      // Metriche performance
      if (typeof window !== "undefined" && window.performance) {
        const navigation = window.performance.getEntriesByType("navigation")[0] as any;
        setPerformanceMetrics({
          loadTime: navigation?.loadEventEnd - navigation?.navigationStart || 0,
          domContentLoaded: navigation?.domContentLoadedEventEnd - navigation?.navigationStart || 0,
          memoryUsed: (window.performance as any).memory?.usedJSHeapSize || 0,
          memoryTotal: (window.performance as any).memory?.totalJSHeapSize || 0,
        });
      }
    };

    updateData();
    const interval = setInterval(updateData, 1000);
    return () => clearInterval(interval);
  }, [isOpen]);

  if (!isOpen) return null;

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms.toFixed(0)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const exportLogs = () => {
    const logText = logger.exportLogs();
    const blob = new Blob([logText], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `maraffa-logs-${new Date().toISOString().split("T")[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-2">
            <Bug className="w-5 h-5 text-blue-600" />
            <h2 className="text-lg font-bold text-gray-900">Debug Panel</h2>
          </div>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b">
          {[
            { id: "logs", label: "Logs", icon: Eye },
            { id: "cache", label: "Cache", icon: Database },
            { id: "game", label: "Game State", icon: Cpu },
            { id: "performance", label: "Performance", icon: Bug },
          ].map(({ id, label, icon: Icon }) => (
            <button
              key={id}
              onClick={() => setActiveTab(id as any)}
              className={`flex items-center gap-2 px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                activeTab === id
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700"
              }`}
            >
              <Icon className="w-4 h-4" />
              {label}
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-4">
          {activeTab === "logs" && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <h3 className="font-semibold">Recent Logs ({logs.length})</h3>
                <button
                  onClick={exportLogs}
                  className="flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
                >
                  <Download className="w-4 h-4" />
                  Export
                </button>
              </div>
              <div className="space-y-2 max-h-96 overflow-auto">
                {logs.map((log, index) => (
                  <div
                    key={index}
                    className={`p-2 rounded text-xs font-mono border-l-4 ${
                      log.level === 0 ? "border-gray-400 bg-gray-50" :
                      log.level === 1 ? "border-blue-400 bg-blue-50" :
                      log.level === 2 ? "border-yellow-400 bg-yellow-50" :
                      log.level === 3 ? "border-red-400 bg-red-50" :
                      "border-red-600 bg-red-100"
                    }`}
                  >
                    <div className="flex justify-between items-start mb-1">
                      <span className="font-bold">[{log.category}]</span>
                      <span className="text-gray-500">
                        {new Date(log.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                    <div>{log.message}</div>
                    {log.data && (
                      <details className="mt-1">
                        <summary className="cursor-pointer text-gray-600">Data</summary>
                        <pre className="mt-1 p-2 bg-gray-100 rounded text-xs overflow-auto">
                          {JSON.stringify(log.data, null, 2)}
                        </pre>
                      </details>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === "cache" && (
            <div className="space-y-6">
              <div>
                <h3 className="font-semibold mb-2">Image Cache</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>Hit Rate: {cacheStats.images?.hitRate || 0}%</div>
                  <div>Size: {cacheStats.images?.size || 0}/{cacheStats.images?.maxSize || 0}</div>
                  <div>Hits: {cacheStats.images?.hits || 0}</div>
                  <div>Misses: {cacheStats.images?.misses || 0}</div>
                  <div>Evictions: {cacheStats.images?.evictions || 0}</div>
                </div>
              </div>
              
              <div>
                <h3 className="font-semibold mb-2">API Cache</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>Hit Rate: {cacheStats.api?.hitRate || 0}%</div>
                  <div>Size: {cacheStats.api?.size || 0}/{cacheStats.api?.maxSize || 0}</div>
                  <div>Hits: {cacheStats.api?.hits || 0}</div>
                  <div>Misses: {cacheStats.api?.misses || 0}</div>
                  <div>Evictions: {cacheStats.api?.evictions || 0}</div>
                </div>
              </div>
            </div>
          )}

          {activeTab === "game" && (
            <div>
              <h3 className="font-semibold mb-2">Game State</h3>
              <pre className="bg-gray-100 p-4 rounded text-xs overflow-auto max-h-96">
                {JSON.stringify(gameState, null, 2)}
              </pre>
            </div>
          )}

          {activeTab === "performance" && (
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">Load Performance</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>Page Load: {formatTime(performanceMetrics.loadTime)}</div>
                  <div>DOM Ready: {formatTime(performanceMetrics.domContentLoaded)}</div>
                </div>
              </div>
              
              <div>
                <h3 className="font-semibold mb-2">Memory Usage</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>Used: {formatBytes(performanceMetrics.memoryUsed)}</div>
                  <div>Total: {formatBytes(performanceMetrics.memoryTotal)}</div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DebugPanel;
