# 📚 Documentazione - Marafone Romagnolo

Benvenuti nella documentazione completa del progetto **Marafone Romagnolo**, un'implementazione digitale del tradizionale gioco di carte romagnolo sviluppato con React, TypeScript e tecnologie moderne.

---

## 🗂️ Indice della Documentazione

### 📋 **Panoramica del Progetto**

- **[APP_OVERVIEW.md](./APP_OVERVIEW.md)** - Panoramica completa dell'applicazione, funzionalità e caratteristiche principali

### 🏗️ **Architettura e Sviluppo**

- **[GUIDA_REPOSITORY.md](./GUIDA_REPOSITORY.md)** - Guida completa all'architettura, stack tecnologico e organizzazione del codice
- **[CPU_LOGIC_DOCUMENTATION.md](./CPU_LOGIC_DOCUMENTATION.md)** - Documentazione dettagliata della logica AI e strategie di gioco
- **[AI_RULES_IMPLEMENTATION_REPORT.md](./AI_RULES_IMPLEMENTATION_REPORT.md)** - Report sull'implementazione delle regole AI

### 🎮 **Logica di Gioco**

- **[logica-di-gioco.md](./logica-di-gioco.md)** - Regole complete della Marafone Romagnolo e implementazione digitale

### 🎨 **Assets e Risorse**

- **[IMAGE_ASSETS.md](./IMAGE_ASSETS.md)** - Organizzazione e convenzioni per immagini, carte e loghi

### 📱 **Pubblicazione e Store**

- **[PLAY_STORE_LISTING.md](./PLAY_STORE_LISTING.md)** - Informazioni per la pubblicazione su Google Play Store
- **[IOS_SETUP_GUIDE.md](./IOS_SETUP_GUIDE.md)** - Guida per la configurazione e pubblicazione iOS
- **[IPHONE.md](./IPHONE.md)** - Specifiche e adattamenti per dispositivi iPhone

### 🔐 **Sicurezza e Integrazione**

- **[SECURITY_GUIDE.md](./SECURITY_GUIDE.md)** - Guida alla sicurezza e best practices
- **[OAUTH_SETUP.md](./OAUTH_SETUP.md)** - Configurazione autenticazione OAuth
- **[ADMOB_INTEGRATION.md](./ADMOB_INTEGRATION.md)** - Integrazione AdMob per pubblicità
- **[PREMIUM_SUBSCRIPTION_SETUP.md](./PREMIUM_SUBSCRIPTION_SETUP.md)** - Setup abbonamenti premium

### 📄 **Legale e Privacy**

- **[PRIVACY_POLICY.md](./PRIVACY_POLICY.md)** - Policy sulla privacy e gestione dati utente
- **[TERMS_OF_SERVICE.md](./TERMS_OF_SERVICE.md)** - Termini di servizio dell'applicazione

---

## 🚀 Guida Rapida per Iniziare

### Per **Nuovi Sviluppatori**:

1. 📖 Inizia con **[APP_OVERVIEW.md](./APP_OVERVIEW.md)** per capire cosa fa l'app
2. 🏗️ Leggi **[GUIDA_REPOSITORY.md](./GUIDA_REPOSITORY.md)** per l'architettura del progetto
3. 🎮 Consulta **[logica-di-gioco.md](./logica-di-gioco.md)** per le regole implementate
4. 🤖 Studia **[CPU_LOGIC_DOCUMENTATION.md](./CPU_LOGIC_DOCUMENTATION.md)** per capire l'AI

### Per **Contribuitori**:

1. 🎨 Vedi **[IMAGE_ASSETS.md](./IMAGE_ASSETS.md)** per gestire assets grafici
2. 🔐 Consulta **[SECURITY_GUIDE.md](./SECURITY_GUIDE.md)** per le best practices di sicurezza
3. 🤖 Leggi **[AI_RULES_IMPLEMENTATION_REPORT.md](./AI_RULES_IMPLEMENTATION_REPORT.md)** per l'implementazione AI

### Per **Publishing Android**:

1. 🏪 Usa **[PLAY_STORE_LISTING.md](./PLAY_STORE_LISTING.md)** per il Play Store
2. 📱 Configura **[ADMOB_INTEGRATION.md](./ADMOB_INTEGRATION.md)** per le pubblicità
3. 💰 Setup **[PREMIUM_SUBSCRIPTION_SETUP.md](./PREMIUM_SUBSCRIPTION_SETUP.md)** per abbonamenti
4. 🔒 Includi **[PRIVACY_POLICY.md](./PRIVACY_POLICY.md)** e **[TERMS_OF_SERVICE.md](./TERMS_OF_SERVICE.md)**

### Per **Publishing iOS**:

1. 🍎 Segui **[IOS_SETUP_GUIDE.md](./IOS_SETUP_GUIDE.md)** per la configurazione iOS
2. 📱 Consulta **[IPHONE.md](./IPHONE.md)** per adattamenti specifici iPhone
3. 🔐 Configura **[OAUTH_SETUP.md](./OAUTH_SETUP.md)** per l'autenticazione

---

## 🎯 Caratteristiche del Progetto

### ✨ **Tecnologie Principali**

- **React 18** + **TypeScript** per l'interfaccia utente
- **Tailwind CSS** + **shadcn/ui** per lo styling
- **Vite** per il build system
- **Capacitor** per l'app nativa Android

### 🎮 **Funzionalità**

- Gioco completo del Marafone Romagnolo con regole autentiche
- Sistema AI con 3 livelli di difficoltà
- Design responsive e ottimizzazioni mobile
- Sistema avanzato di cache per le performance
- PWA ready con installazione nativa

### 📊 **Prestazioni**

- Caricamento immagini ottimizzato con cache intelligente
- Precaricamento adattivo basato su dispositivo e connessione
- Bundle splitting e lazy loading per tempi di caricamento veloci

---

## 📝 Standard di Documentazione

### Convenzioni Utilizzate

- **📁 File markdown** organizzati per argomento
- **🔗 Link interni** tra documenti correlati
- **📋 Tabelle** per informazioni strutturate
- **💡 Esempi pratici** per illustrare concetti
- **⚠️ Note importanti** evidenziate chiaramente

### Mantenimento

- ✅ Documentazione aggiornata ad ogni release
- ✅ Esempi di codice testati e funzionanti
- ✅ Link verificati e funzionanti
- ✅ Versioning sincronizzato con il codice

---

## 🔗 Riferimenti Utili

### Documentazione Tecnica

- [React Documentation](https://react.dev/) - Libreria UI principale
- [TypeScript Handbook](https://www.typescriptlang.org/docs/) - Type safety
- [Tailwind CSS](https://tailwindcss.com/docs) - Framework CSS
- [Vite Guide](https://vitejs.dev/guide/) - Build tool

### Deployment e Mobile

- [Capacitor Documentation](https://capacitorjs.com/docs) - Wrapper nativo
- [Android Developer Guide](https://developer.android.com/docs) - Sviluppo Android
- [PWA Guidelines](https://web.dev/progressive-web-apps/) - Progressive Web Apps

---

_Ultima modifica: 21 luglio 2025 | Versione documentazione: 2.1_
