import { Card as CardType } from "@/utils/game/cardUtils";
import Card from "./Card";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";
import { isDebugModeEnabled } from "@/utils/debug/debugMode";
import { usePlayerIcon } from "@/hooks/usePlayerIcon";

interface PlayerHandProps {
  cards: CardType[];
  isCurrentPlayer: boolean;
  isHuman: boolean;
  playableCards?: CardType[];
  onCardSelect: (card: CardType) => void;
  position: "bottom" | "left" | "top" | "right";
  playerName: string;
  teamId: number;
  hideCards?: boolean;
  trumpSuit?: string;
  cardSize: string; // Aggiunta la proprietà cardSize
  getCardMaraffaStatus?: (card: CardType) => boolean; // Function to determine if card should show Maraffa indicator
  gamePhase?: string; // Aggiungi gamePhase per controllare l'opacità
}

const PlayerHand = ({
  cards,
  isCurrentPlayer,
  isHuman,
  playableCards = [],
  onCardSelect,
  position,
  playerName,
  teamId,
  hideCards = false,
  trumpSuit,
  cardSize, // Usata la nuova proprietà
  getCardMaraffaStatus, // Function to check if card should show Maraffa indicator
  gamePhase, // Aggiungi gamePhase
}: PlayerHandProps) => {
  const isMobile = useIsMobile();
  const { getPlayerIcon } = usePlayerIcon();
  const playableCardIds = playableCards.map((card) => card.id);

  const sortedCards = [...cards].sort((a, b) => {
    if (a.suit === b.suit) {
      return b.order - a.order;
    }
    return a.suit.localeCompare(b.suit);
  });

  // Ottieni informazioni del rank se è il giocatore "Tu"
  // const rankInfo = getPlayerRankInfo(playerName); // Unused for now

  // Improved player positioning for better visibility on both mobile and desktop
  const handPositionClasses = {
    bottom: "bottom-4 left-1/2 -translate-x-1/2",
    top: "top-28 left-1/2 -translate-x-1/2",
    left: isMobile
      ? "left-0 top-[32%] -translate-y-1/2"
      : "left-16 top-1/2 -translate-y-1/2", // Further improved for desktop visibility
    right: isMobile
      ? "right-0 top-[54%] -translate-y-1/2"
      : "right-16 top-1/2 -translate-y-1/2", // Further improved for desktop visibility
  };
  const getPlayerInfoPositionClasses = () => {
    switch (position) {
      case "bottom":
        // "Tu" - APPENA FUORI dal bordo sud del tappetino
        return "bottom-full mb-3 left-1/2 -translate-x-1/2";
      case "top":
        // "Player 3" - APPENA FUORI dal bordo nord del tappetino
        return "top-full mt-3 left-1/2 -translate-x-1/2";
      case "left":
        return isMobile
          ? "left-full ml-1 top-1/2 -translate-y-1/2"
          : "left-full ml-6 top-1/2 -translate-y-1/2";
      case "right":
        return isMobile
          ? "right-full mr-1 top-1/2 -translate-y-1/2"
          : "right-full mr-6 top-1/2 -translate-y-1/2";
      default:
        return "";
    }
  };

  // Enhanced hand width for better card visibility - ensuring they appear in desktop mode
  const getHandWidthClass = () => {
    const cardCount = sortedCards.length;

    // Handle mobile layout differently
    if (isMobile) {
      switch (position) {
        case "left":
        case "right":
          return "h-24 w-16";
        case "bottom":
        case "top":
          return cardCount <= 3 ? "h-28 w-80" : "w-[300px] h-28";
        default:
          return "h-28 w-80";
      }
    } else {
      // Desktop layout with significantly improved visibility
      switch (position) {
        case "left":
        case "right":
          return "h-32 w-32"; // Increased size for much better visibility
        case "bottom":
          // Increased space for player's cards - much larger area for desktop
          return cardCount <= 3
            ? "h-32 w-[400px]"
            : cardCount <= 5
            ? "h-32 w-[500px]"
            : "h-32 w-[600px]";
        case "top":
          return cardCount <= 3
            ? "h-32 w-[340px]"
            : cardCount <= 5
            ? "h-32 w-[400px]"
            : "h-32 w-[480px]";
        default:
          return "h-32 w-[340px]";
      }
    }
  };

  const shouldUseGridLayout = isMobile && position === "bottom";

  const getGridLayout = () => {
    const totalCards = sortedCards.length;
    if (totalCards <= 5) return { rows: 1, cols: totalCards };
    return { rows: 2, cols: Math.ceil(totalCards / 2) };
  };

  const gridLayout = getGridLayout();

  return (
    <div
      className={cn(
        "absolute pointer-events-auto",
        handPositionClasses[position],
        isCurrentPlayer ? "z-20" : "z-10"
      )}
    >
      <div className="relative">
        {" "}
        {/* Info del giocatore con design migliorato */}
        <div
          className={cn(
            "player-info-container absolute px-1.5 py-1 rounded-lg glass border-2 shadow-lg z-10 flex items-center gap-1.5 backdrop-blur-md player-info-transition min-w-fit max-w-[200px]",
            getPlayerInfoPositionClasses(),
            teamId === 0
              ? "border-amber-500 bg-gradient-to-r from-amber-50/95 to-yellow-100/95"
              : "border-red-500 bg-gradient-to-r from-red-50/95 to-rose-100/95",
            isCurrentPlayer
              ? "ring-2 ring-blue-400 ring-opacity-60 scale-105 current-player-glow"
              : ""
          )}
        >
          {/* Icona del giocatore */}
          {getPlayerIcon(isHuman, teamId, playerName, isCurrentPlayer)}
          {/* Informazioni testo - nome con gestione overflow intelligente */}
          <span
            className={cn(
              "text-xs font-bold leading-tight flex-1 min-w-0",
              isCurrentPlayer
                ? "text-white" // Bianco quando è il turno (ha bg blu)
                : teamId === 0
                ? "text-amber-800"
                : "text-red-800"
            )}
            title={playerName} // Tooltip per vedere il nome completo
          >
            {/* Tronca il nome se troppo lungo, ma mantieni leggibilità */}
            {playerName.length > 12
              ? `${playerName.substring(0, 10)}...`
              : playerName}
          </span>
        </div>
        {/* Always ensure cards are visible unless explicitly hidden */}
        {!hideCards && (
          <div
            className={cn(
              "relative transition-all duration-300 ease-in-out flex justify-center",
              shouldUseGridLayout ? "" : getHandWidthClass()
            )}
          >
            {" "}
            {shouldUseGridLayout ? (
              // Grid layout for mobile - no rotation, no overlap, centered and responsive
              <div
                className="grid gap-2 mx-auto justify-center"
                style={{
                  display: "grid",
                  gridTemplateColumns: `repeat(${gridLayout.cols}, minmax(60px, 1fr))`,
                  gridTemplateRows: `repeat(${gridLayout.rows}, auto)`,
                  width: "fit-content",
                  maxWidth: "95vw",
                  justifyItems: "center",
                  padding: "4px",
                }}
              >
                {sortedCards.map((card) => {
                  const isPlayable =
                    isHuman &&
                    isCurrentPlayer &&
                    playableCardIds.includes(card.id);
                  const nonPlayable =
                    isHuman &&
                    isCurrentPlayer &&
                    !playableCardIds.includes(card.id) &&
                    playableCardIds.length > 0 &&
                    gamePhase !== "selectTrump"; // Non rendere opache le carte durante la selezione briscola

                  const isTrump = trumpSuit && card.suit === trumpSuit;
                  const hasMaraffa = getCardMaraffaStatus
                    ? getCardMaraffaStatus(card)
                    : false;

                  return (
                    <div
                      key={card.id}
                      className={cn(
                        "flex items-center justify-center",
                        nonPlayable ? "opacity-40" : "opacity-100"
                      )}
                    >
                      {" "}
                      <div
                        className={cn(
                          "transition-all duration-300",
                          isHuman && isCurrentPlayer && isPlayable
                            ? "drop-shadow-[0_0_15px_rgba(255,215,0,0.9)] drop-shadow-[0_0_25px_rgba(255,215,0,0.6)]" // aura dorata più intensa
                            : isHuman && isCurrentPlayer
                            ? "drop-shadow-[0_0_8px_rgba(37,99,235,0.7)]" // blu più scuro per carte non giocabili
                            : ""
                        )}
                      >
                        <Card
                          key={card.id}
                          card={card}
                          isPlayable={isPlayable}
                          isRevealed={isHuman || isDebugModeEnabled()}
                          onClick={() => isPlayable && onCardSelect(card)}
                          scale="sm"
                          isTrump={isTrump}
                          hasMaraffa={hasMaraffa}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              // Fixed layout with improved positioning for desktop
              <div className="relative h-full w-full">
                {sortedCards.map((card, index) => {
                  const isPlayable =
                    isHuman &&
                    isCurrentPlayer &&
                    playableCardIds.includes(card.id);
                  const nonPlayable =
                    isHuman &&
                    isCurrentPlayer &&
                    !playableCardIds.includes(card.id) &&
                    playableCardIds.length > 0 &&
                    gamePhase !== "selectTrump"; // Non rendere opache le carte durante la selezione briscola

                  const totalCards = sortedCards.length;

                  const isTrump = trumpSuit && card.suit === trumpSuit;
                  const hasMaraffa = getCardMaraffaStatus
                    ? getCardMaraffaStatus(card)
                    : false;

                  // Enhanced spacing between cards for much better visibility on desktop
                  const spacing =
                    position === "bottom" || position === "top"
                      ? isMobile
                        ? Math.min(60, 350 / Math.max(totalCards, 1))
                        : position === "bottom"
                        ? Math.min(95, 580 / Math.max(totalCards, 1)) // More space for human player cards
                        : Math.min(85, 480 / Math.max(totalCards, 1))
                      : isMobile
                      ? Math.min(28, 180 / Math.max(totalCards, 1))
                      : Math.min(45, 260 / Math.max(totalCards, 1)); // Better spacing for side players

                  // Calculate position of each card with improved visibility
                  const offset =
                    position === "bottom" || position === "top"
                      ? `calc(${index - totalCards / 2} * ${spacing}px)`
                      : `calc(${index - totalCards / 2} * ${spacing}px)`;

                  const translateX =
                    position === "bottom" || position === "top" ? offset : "0";
                  const translateY = !(
                    position === "bottom" || position === "top"
                  )
                    ? offset
                    : "0";
                  const rotate =
                    position === "left" ? -90 : position === "right" ? 90 : 0; // Unifico le scale per coerenza con le carte giocate (che usano "md")
                  const cardScale = !isMobile ? "md" : "sm";

                  return (
                    <div
                      key={card.id}
                      className={cn(
                        "absolute transition-all duration-300 ease-in-out",
                        position === "bottom" || position === "top"
                          ? "left-1/2 -translate-x-1/2"
                          : "",
                        !(position === "bottom" || position === "top")
                          ? "top-1/2 -translate-y-1/2"
                          : "",
                        nonPlayable ? "opacity-40" : "opacity-100",
                        isHuman && isCurrentPlayer && isPlayable
                          ? "drop-shadow-[0_0_15px_rgba(255,215,0,0.9)] drop-shadow-[0_0_25px_rgba(255,215,0,0.6)]" // aura dorata più intensa
                          : isHuman && isCurrentPlayer
                          ? "drop-shadow-[0_0_8px_rgba(37,99,235,0.7)]" // blu più scuro per carte non giocabili
                          : ""
                      )}
                      style={{
                        transform: `translate(${translateX}, ${translateY}) rotate(${rotate}deg)`,
                        zIndex: index,
                      }}
                    >
                      <Card
                        key={card.id}
                        card={card}
                        isPlayable={isPlayable}
                        isRevealed={isHuman || isDebugModeEnabled()}
                        onClick={() => isPlayable && onCardSelect(card)}
                        scale={cardScale}
                        isTrump={isTrump}
                        hasMaraffa={hasMaraffa}
                      />
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default PlayerHand;
