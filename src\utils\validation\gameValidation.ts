import { Card, Suit } from "@/utils/game/cardUtils";
import { GameState } from "@/utils/game/gameLogic";

/**
 * 🔍 Utility per la validazione dei dati di gioco
 * Assicura l'integrità dei dati e previene errori
 */

// Tipi per i risultati di validazione
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Valida una carta
 */
export const validateCard = (card: any): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!card) {
    errors.push("Carta non definita");
    return { isValid: false, errors, warnings };
  }

  // Controlla proprietà obbligatorie
  if (!card.id) errors.push("ID carta mancante");
  if (!card.suit) errors.push("Seme carta mancante");
  if (!card.rank) errors.push("Valore carta mancante");

  // Controlla valori validi
  const validSuits: Suit[] = ["bastoni", "coppe", "denari", "spade"];
  const validRanks = ["A", "2", "3", "4", "5", "6", "7", "J", "Q", "K"];

  if (card.suit && !validSuits.includes(card.suit)) {
    errors.push(`Seme non valido: ${card.suit}`);
  }

  if (card.rank && !validRanks.includes(card.rank)) {
    errors.push(`Valore non valido: ${card.rank}`);
  }

  // Avvertimenti
  if (card.id && typeof card.id !== "string") {
    warnings.push("ID carta dovrebbe essere una stringa");
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * Valida un array di carte (mano o mazzo)
 */
export const validateCardArray = (cards: any[], context = "carte"): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!Array.isArray(cards)) {
    errors.push(`${context} deve essere un array`);
    return { isValid: false, errors, warnings };
  }

  // Controlla ogni carta
  cards.forEach((card, index) => {
    const cardValidation = validateCard(card);
    if (!cardValidation.isValid) {
      errors.push(`Carta ${index + 1}: ${cardValidation.errors.join(", ")}`);
    }
    warnings.push(...cardValidation.warnings.map(w => `Carta ${index + 1}: ${w}`));
  });

  // Controlla duplicati
  const cardIds = cards.map(card => card?.id).filter(Boolean);
  const uniqueIds = new Set(cardIds);
  if (cardIds.length !== uniqueIds.size) {
    errors.push("Carte duplicate trovate");
  }

  // Avvertimenti per dimensioni inusuali
  if (cards.length > 40) {
    warnings.push("Numero di carte superiore al normale (>40)");
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * Valida lo stato del gioco
 */
export const validateGameState = (gameState: any): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!gameState) {
    errors.push("Stato gioco non definito");
    return { isValid: false, errors, warnings };
  }

  // Controlla proprietà obbligatorie
  if (!gameState.players) errors.push("Giocatori non definiti");
  if (gameState.currentPlayer === undefined) errors.push("Giocatore corrente non definito");
  if (!gameState.gamePhase) errors.push("Fase di gioco non definita");

  // Valida giocatori
  if (gameState.players) {
    if (!Array.isArray(gameState.players)) {
      errors.push("Giocatori deve essere un array");
    } else {
      if (gameState.players.length !== 4) {
        errors.push("Devono esserci esattamente 4 giocatori");
      }

      gameState.players.forEach((player: any, index: number) => {
        if (!player) {
          errors.push(`Giocatore ${index + 1} non definito`);
          return;
        }

        if (player.id === undefined) errors.push(`Giocatore ${index + 1}: ID mancante`);
        if (!player.name) errors.push(`Giocatore ${index + 1}: Nome mancante`);
        if (player.team === undefined) errors.push(`Giocatore ${index + 1}: Team mancante`);

        // Valida mano del giocatore
        if (player.hand) {
          const handValidation = validateCardArray(player.hand, `mano giocatore ${index + 1}`);
          if (!handValidation.isValid) {
            errors.push(...handValidation.errors);
          }
          warnings.push(...handValidation.warnings);
        }
      });
    }
  }

  // Valida giocatore corrente
  if (gameState.currentPlayer !== undefined && gameState.players) {
    if (gameState.currentPlayer < 0 || gameState.currentPlayer >= gameState.players.length) {
      errors.push("Indice giocatore corrente non valido");
    }
  }

  // Valida fase di gioco
  const validPhases = ["trumpSelection", "playing", "gameOver"];
  if (gameState.gamePhase && !validPhases.includes(gameState.gamePhase)) {
    errors.push(`Fase di gioco non valida: ${gameState.gamePhase}`);
  }

  // Valida briscola
  if (gameState.trumpSuit) {
    const validSuits: Suit[] = ["bastoni", "coppe", "denari", "spade"];
    if (!validSuits.includes(gameState.trumpSuit)) {
      errors.push(`Briscola non valida: ${gameState.trumpSuit}`);
    }
  }

  // Valida presa corrente
  if (gameState.currentTrick) {
    const trickValidation = validateCardArray(gameState.currentTrick, "presa corrente");
    if (!trickValidation.isValid) {
      errors.push(...trickValidation.errors);
    }
    warnings.push(...trickValidation.warnings);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * Valida una mossa di gioco
 */
export const validateMove = (
  card: Card,
  gameState: GameState,
  playerIndex: number
): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Valida carta
  const cardValidation = validateCard(card);
  if (!cardValidation.isValid) {
    errors.push(...cardValidation.errors);
  }

  // Controlla se è il turno del giocatore
  if (gameState.currentPlayer !== playerIndex) {
    errors.push("Non è il turno di questo giocatore");
  }

  // Controlla se il giocatore ha la carta
  const player = gameState.players[playerIndex];
  if (player && !player.hand.some(c => c.id === card.id)) {
    errors.push("Il giocatore non ha questa carta");
  }

  // Controlla fase di gioco
  if (gameState.gamePhase !== "playing") {
    errors.push("Non è possibile giocare carte in questa fase");
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * Utility per loggare i risultati di validazione
 */
export const logValidationResult = (result: ValidationResult, context = "Validazione") => {
  if (!result.isValid) {
    console.error(`❌ ${context} fallita:`, result.errors);
  }
  
  if (result.warnings.length > 0) {
    console.warn(`⚠️ ${context} avvertimenti:`, result.warnings);
  }
  
  if (result.isValid && result.warnings.length === 0) {
    console.log(`✅ ${context} completata con successo`);
  }
};
