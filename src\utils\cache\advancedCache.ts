/**
 * 🚀 Sistema di Cache Avanzato con TTL e LRU
 * Supporta Time-To-Live, Least Recently Used eviction, e statistiche
 */

interface CacheEntry<T> {
  value: T;
  timestamp: number;
  ttl?: number; // Time To Live in milliseconds
  accessCount: number;
  lastAccessed: number;
  priority: number; // 0 = bassa, 10 = alta
}

interface CacheStats {
  hits: number;
  misses: number;
  evictions: number;
  size: number;
  maxSize: number;
}

export class AdvancedCache<T> {
  private cache = new Map<string, CacheEntry<T>>();
  private maxSize: number;
  private defaultTTL?: number;
  private stats: CacheStats;
  private cleanupInterval?: NodeJS.Timeout;

  constructor(maxSize = 100, defaultTTL?: number) {
    this.maxSize = maxSize;
    this.defaultTTL = defaultTTL;
    this.stats = {
      hits: 0,
      misses: 0,
      evictions: 0,
      size: 0,
      maxSize,
    };

    // Cleanup automatico ogni 5 minuti
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  /**
   * Ottieni un valore dalla cache
   */
  get(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      this.stats.misses++;
      return null;
    }

    // Controlla TTL
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      this.stats.misses++;
      this.stats.evictions++;
      this.updateSize();
      return null;
    }

    // Aggiorna statistiche di accesso
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    this.stats.hits++;

    return entry.value;
  }

  /**
   * Imposta un valore nella cache
   */
  set(key: string, value: T, options?: { ttl?: number; priority?: number }): void {
    const now = Date.now();
    const ttl = options?.ttl ?? this.defaultTTL;
    const priority = options?.priority ?? 5;

    const entry: CacheEntry<T> = {
      value,
      timestamp: now,
      ttl,
      accessCount: 1,
      lastAccessed: now,
      priority,
    };

    // Se la cache è piena, rimuovi elementi
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      this.evictLRU();
    }

    this.cache.set(key, entry);
    this.updateSize();
  }

  /**
   * Controlla se una chiave esiste ed è valida
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      this.stats.evictions++;
      this.updateSize();
      return false;
    }
    
    return true;
  }

  /**
   * Rimuovi una chiave dalla cache
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted) {
      this.updateSize();
    }
    return deleted;
  }

  /**
   * Pulisci la cache da elementi scaduti
   */
  cleanup(): number {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        this.cache.delete(key);
        cleaned++;
        this.stats.evictions++;
      }
    }

    this.updateSize();
    
    if (cleaned > 0) {
      console.log(`🧹 Cache cleanup: rimossi ${cleaned} elementi scaduti`);
    }
    
    return cleaned;
  }

  /**
   * Svuota completamente la cache
   */
  clear(): void {
    this.cache.clear();
    this.stats.evictions += this.stats.size;
    this.updateSize();
  }

  /**
   * Ottieni statistiche della cache
   */
  getStats(): CacheStats & { hitRate: number } {
    const total = this.stats.hits + this.stats.misses;
    const hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0;
    
    return {
      ...this.stats,
      hitRate: Math.round(hitRate * 100) / 100,
    };
  }

  /**
   * Ottieni tutte le chiavi nella cache
   */
  keys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * Ottieni informazioni dettagliate su una chiave
   */
  getEntryInfo(key: string): Omit<CacheEntry<T>, 'value'> | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    const { value, ...info } = entry;
    return {
      ...info,
      isExpired: this.isExpired(entry),
      ageMs: Date.now() - entry.timestamp,
    } as any;
  }

  /**
   * Distruggi la cache e pulisci risorse
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.clear();
  }

  // Metodi privati

  private isExpired(entry: CacheEntry<T>): boolean {
    if (!entry.ttl) return false;
    return Date.now() - entry.timestamp > entry.ttl;
  }

  private evictLRU(): void {
    let oldestKey: string | null = null;
    let oldestTime = Infinity;
    let lowestPriority = Infinity;

    // Trova l'elemento meno recentemente usato con priorità più bassa
    for (const [key, entry] of this.cache.entries()) {
      const score = entry.lastAccessed - (entry.priority * 1000 * 60 * 60); // Priorità in ore
      
      if (score < oldestTime) {
        oldestTime = score;
        oldestKey = key;
        lowestPriority = entry.priority;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      this.stats.evictions++;
      console.log(`🗑️ Cache LRU eviction: rimossa chiave "${oldestKey}" (priorità: ${lowestPriority})`);
    }
  }

  private updateSize(): void {
    this.stats.size = this.cache.size;
  }
}

// Istanza globale per immagini
export const imageCache = new AdvancedCache<HTMLImageElement>(200, 30 * 60 * 1000); // 30 minuti TTL

// Istanza globale per dati API
export const apiCache = new AdvancedCache<any>(50, 5 * 60 * 1000); // 5 minuti TTL

// Istanza globale per configurazioni
export const configCache = new AdvancedCache<any>(20); // Nessun TTL per configurazioni
