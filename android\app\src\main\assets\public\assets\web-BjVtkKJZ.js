import{W as n}from"./game-DyVU82mC.js";import"./audioManager-oMWoQbcF.js";import"./audio-5UOY9KLB.js";import"./vendor-COrNHRvO.js";class y extends n{constructor(){super(),this.not_supported_mssg="This method is not supported",this.options_missing_mssg="Firebase options are missing",this.duplicate_app_mssg="Firebase app already exists",this.analytics_missing_mssg="Firebase analytics is not initialized. Make sure initializeFirebase() is called once",this.scripts=[{key:"firebase-app",src:"https://www.gstatic.com/firebasejs/8.2.3/firebase-app.js"},{key:"firebase-ac",src:"https://www.gstatic.com/firebasejs/8.2.3/firebase-analytics.js"}],this.ready=new Promise(s=>this.readyResolver=s),this.configure()}initializeFirebase(s){return new Promise(async(e,i)=>{if(await this.ready,this.hasFirebaseInitialized()){i(this.duplicate_app_mssg);return}if(!s){i(this.options_missing_mssg);return}const t=window.firebase.initializeApp(s);this.analyticsRef=t.analytics(),e(this.analyticsRef)})}setUserId(s){return new Promise(async(e,i)=>{if(await this.ready,!this.analyticsRef){i(this.analytics_missing_mssg);return}const{userId:t}=s||{userId:void 0};if(!t){i("userId property is missing");return}this.analyticsRef.setUserId(t),e()})}setUserProperty(s){return new Promise(async(e,i)=>{if(await this.ready,!this.analyticsRef){i(this.analytics_missing_mssg);return}const{name:t,value:a}=s||{name:void 0,value:void 0};if(!t){i("name property is missing");return}if(!a){i("value property is missing");return}let r={};r[t]=a,this.analyticsRef.setUserProperties(r),e()})}getAppInstanceId(){return new Promise((s,e)=>s)}setScreenName(s){return new Promise((e,i)=>e)}reset(){return new Promise((s,e)=>s)}logEvent(s){return new Promise(async(e,i)=>{if(await this.ready,!this.analyticsRef){i(this.analytics_missing_mssg);return}const{name:t,params:a}=s||{name:void 0,params:void 0};if(!t){i("name property is missing");return}this.analyticsRef.logEvent(t,a),e()})}setCollectionEnabled(s){return new Promise(async(e,i)=>{if(await this.ready,!this.analyticsRef){i(this.analytics_missing_mssg);return}const{enabled:t}=s||{enabled:!1};this.analyticsRef.setAnalyticsCollectionEnabled(t),e()})}setSessionTimeoutDuration(s){return new Promise((e,i)=>{i(this.not_supported_mssg)})}get remoteConfig(){return this.analyticsRef}enable(){return new Promise(async(s,e)=>{if(await this.ready,!this.analyticsRef){e(this.analytics_missing_mssg);return}this.analyticsRef.setAnalyticsCollectionEnabled(!0),s()})}disable(){return new Promise(async(s,e)=>{if(await this.ready,!this.analyticsRef){e(this.analytics_missing_mssg);return}this.analyticsRef.setAnalyticsCollectionEnabled(!1),s()})}async configure(){try{await this.loadScripts(),window.firebase&&window.firebase.analytics&&this.hasFirebaseInitialized()&&(this.analyticsRef=window.firebase.analytics())}catch(e){throw e}const s=setInterval(()=>{window.firebase&&(clearInterval(s),this.readyResolver())},50)}loadScripts(){const s=this.scripts[0],e=this.scripts[1];return new Promise(async(i,t)=>{const a=this.scripts.map(r=>r.key);if(document.getElementById(a[0])&&document.getElementById(a[1]))return i(null);await this.loadScript(s.key,s.src),await this.loadScript(e.key,e.src),i(null)})}loadScript(s,e){return new Promise((i,t)=>{const a=document.createElement("script");a.type="text/javascript",a.src=e,a.id=s,a.onload=i,a.onerror=t,document.querySelector("head").appendChild(a)})}hasFirebaseInitialized(){if(!window.firebase)return!1;const s=window.firebase.apps;return!(s&&s.length===0)}}export{y as FirebaseAnalyticsWeb};
